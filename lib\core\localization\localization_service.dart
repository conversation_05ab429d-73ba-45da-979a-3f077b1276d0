import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

/// Service for managing app localization
class LocalizationService {
  static const String _languageKey = AppConstants.languageKey;
  static const String _defaultLanguage = AppConstants.defaultLanguage;
  
  static LocalizationService? _instance;
  static LocalizationService get instance => _instance ??= LocalizationService._();
  
  LocalizationService._();
  
  SharedPreferences? _prefs;
  
  /// Initialize the localization service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  /// Get the current locale
  Locale getCurrentLocale() {
    final languageCode = _prefs?.getString(_languageKey) ?? _defaultLanguage;
    return _getLocaleFromLanguageCode(languageCode);
  }
  
  /// Set the current locale
  Future<void> setLocale(Locale locale) async {
    await _prefs?.setString(_languageKey, locale.languageCode);
  }
  
  /// Get supported locales
  List<Locale> getSupportedLocales() {
    return AppConstants.supportedLanguages
        .map((code) => _getLocaleFromLanguageCode(code))
        .toList();
  }
  
  /// Check if locale is supported
  bool isLocaleSupported(Locale locale) {
    return AppConstants.supportedLanguages.contains(locale.languageCode);
  }
  
  /// Get locale from language code
  Locale _getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return const Locale('ar', 'SA');
      case 'en':
        return const Locale('en', 'US');
      default:
        return const Locale('ar', 'SA'); // Default to Arabic
    }
  }
  
  /// Get text direction for locale
  TextDirection getTextDirection(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return TextDirection.rtl;
      case 'en':
        return TextDirection.ltr;
      default:
        return TextDirection.rtl; // Default to RTL for Arabic
    }
  }
  
  /// Check if locale is RTL
  bool isRTL(Locale locale) {
    return getTextDirection(locale) == TextDirection.rtl;
  }
  
  /// Get language name for display
  String getLanguageName(String languageCode, {String? displayLanguage}) {
    final displayLang = displayLanguage ?? getCurrentLocale().languageCode;
    
    if (displayLang == 'ar') {
      switch (languageCode) {
        case 'ar':
          return 'العربية';
        case 'en':
          return 'الإنجليزية';
        default:
          return 'غير معروف';
      }
    } else {
      switch (languageCode) {
        case 'ar':
          return 'Arabic';
        case 'en':
          return 'English';
        default:
          return 'Unknown';
      }
    }
  }
  
  /// Get available languages for selection
  List<LanguageOption> getLanguageOptions() {
    return AppConstants.supportedLanguages.map((code) {
      return LanguageOption(
        code: code,
        name: getLanguageName(code, displayLanguage: code),
        nativeName: getLanguageName(code, displayLanguage: code),
        locale: _getLocaleFromLanguageCode(code),
      );
    }).toList();
  }
  
  /// Switch to next available language
  Future<Locale> switchLanguage() async {
    final currentLocale = getCurrentLocale();
    final supportedLocales = getSupportedLocales();
    final currentIndex = supportedLocales.indexWhere(
      (locale) => locale.languageCode == currentLocale.languageCode,
    );
    
    final nextIndex = (currentIndex + 1) % supportedLocales.length;
    final nextLocale = supportedLocales[nextIndex];
    
    await setLocale(nextLocale);
    return nextLocale;
  }
  
  /// Reset to default language
  Future<Locale> resetToDefault() async {
    final defaultLocale = _getLocaleFromLanguageCode(_defaultLanguage);
    await setLocale(defaultLocale);
    return defaultLocale;
  }
}

/// Language option model for UI
class LanguageOption {
  final String code;
  final String name;
  final String nativeName;
  final Locale locale;
  
  const LanguageOption({
    required this.code,
    required this.name,
    required this.nativeName,
    required this.locale,
  });
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LanguageOption && other.code == code;
  }
  
  @override
  int get hashCode => code.hashCode;
  
  @override
  String toString() => 'LanguageOption(code: $code, name: $name)';
}
