import 'dart:async';
import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/auth_entity.dart';
import '../../domain/entities/user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_remote_datasource.dart';

/// Implementation of AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;

  AuthRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, AuthEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await remoteDataSource.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during sign in: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String username,
  }) async {
    try {
      final result = await remoteDataSource.signUpWithEmailAndPassword(
        email: email,
        password: password,
        fullName: fullName,
        username: username,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during sign up: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> signInWithMagicLink({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await remoteDataSource.signInWithMagicLink(
        email: email,
        redirectTo: redirectTo,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during magic link sign in: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> verifyOTP({
    required String email,
    required String token,
    required String type,
  }) async {
    try {
      final result = await remoteDataSource.verifyOTP(
        email: email,
        token: token,
        type: type,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during OTP verification: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await remoteDataSource.resetPassword(
        email: email,
        redirectTo: redirectTo,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during password reset: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> updatePassword({
    required String newPassword,
    required String accessToken,
  }) async {
    try {
      await remoteDataSource.updatePassword(
        newPassword: newPassword,
        accessToken: accessToken,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during password update: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      await remoteDataSource.signOut();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during sign out: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthEntity?>> getCurrentSession() async {
    try {
      final result = await remoteDataSource.getCurrentSession();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error getting current session: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthEntity>> refreshSession({
    required String refreshToken,
  }) async {
    try {
      final result = await remoteDataSource.refreshSession(
        refreshToken: refreshToken,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during session refresh: $e'));
    }
  }

  @override
  Future<Either<Failure, UserEntity?>> getCurrentUser() async {
    try {
      final result = await remoteDataSource.getCurrentUser();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error getting current user: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final result = await remoteDataSource.isAuthenticated();
      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error checking authentication: $e'));
    }
  }

  @override
  Stream<AuthEntity> get authStateChanges {
    return remoteDataSource.authStateChanges;
  }

  @override
  Future<Either<Failure, AuthEntity>> signInWithOAuth({
    required String provider,
    String? redirectTo,
  }) async {
    try {
      final result = await remoteDataSource.signInWithOAuth(
        provider: provider,
        redirectTo: redirectTo,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during OAuth sign in: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    try {
      await remoteDataSource.deleteAccount();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error during account deletion: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resendEmailConfirmation({
    required String email,
  }) async {
    try {
      await remoteDataSource.resendEmailConfirmation(email: email);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error resending email confirmation: $e'));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> updateUserMetadata({
    required Map<String, dynamic> metadata,
  }) async {
    try {
      final result = await remoteDataSource.updateUserMetadata(
        metadata: metadata,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error updating user metadata: $e'));
    }
  }
}
