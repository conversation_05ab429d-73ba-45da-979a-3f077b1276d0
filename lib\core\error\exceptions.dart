/// Base exception class for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;

  const AppException(this.message, {this.code});

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when there's a server error
class ServerException extends AppException {
  const ServerException(super.message, {super.code});
}

/// Exception thrown when there's a cache error
class CacheException extends AppException {
  const CacheException(super.message, {super.code});
}

/// Exception thrown when there's a network error
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code});
}

/// Exception thrown when there's an authentication error
class AuthException extends AppException {
  const AuthException(super.message, {super.code});
}

/// Exception thrown when there's a validation error
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code});
}

/// Exception thrown when there's a permission error
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code});
}

/// Exception thrown when a resource is not found
class NotFoundException extends AppException {
  const NotFoundException(super.message, {super.code});
}

/// Exception thrown when there's a timeout
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code});
}

/// Exception thrown when there's a format error
class FormatException extends AppException {
  const FormatException(super.message, {super.code});
}
