import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/user_entity.dart';

/// User model for data layer
class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.username,
    required super.fullName,
    required super.email,
    super.avatarUrl,
    super.profession,
    super.bio,
    super.isOrganization = false,
    super.organizationVerified = false,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      username: json['username'] as String,
      fullName: json['full_name'] as String,
      email: json['email'] as String,
      avatarUrl: json['avatar_url'] as String?,
      profession: json['profession'] as String?,
      bio: json['bio'] as String?,
      isOrganization: json['is_organization'] as bool? ?? false,
      organizationVerified: json['organization_verified'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Create UserModel from Supabase User
  factory UserModel.fromSupabaseUser(User user) {
    final metadata = user.userMetadata ?? {};
    return UserModel(
      id: user.id,
      username:
          metadata['username'] as String? ?? user.email?.split('@').first ?? '',
      fullName: metadata['full_name'] as String? ?? '',
      email: user.email ?? '',
      avatarUrl: metadata['avatar_url'] as String?,
      profession: metadata['profession'] as String?,
      bio: metadata['bio'] as String?,
      isOrganization: metadata['is_organization'] as bool? ?? false,
      organizationVerified: metadata['organization_verified'] as bool? ?? false,
      createdAt: DateTime.parse(user.createdAt),
      updatedAt:
          user.updatedAt != null
              ? DateTime.parse(user.updatedAt!)
              : DateTime.parse(user.createdAt),
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'full_name': fullName,
      'email': email,
      'avatar_url': avatarUrl,
      'profession': profession,
      'bio': bio,
      'is_organization': isOrganization,
      'organization_verified': organizationVerified,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create UserModel from UserEntity
  factory UserModel.fromEntity(UserEntity entity) {
    return UserModel(
      id: entity.id,
      username: entity.username,
      fullName: entity.fullName,
      email: entity.email,
      avatarUrl: entity.avatarUrl,
      profession: entity.profession,
      bio: entity.bio,
      isOrganization: entity.isOrganization,
      organizationVerified: entity.organizationVerified,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to UserEntity
  UserEntity toEntity() {
    return UserEntity(
      id: id,
      username: username,
      fullName: fullName,
      email: email,
      avatarUrl: avatarUrl,
      profession: profession,
      bio: bio,
      isOrganization: isOrganization,
      organizationVerified: organizationVerified,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Create a copy with updated fields
  @override
  UserModel copyWith({
    String? id,
    String? username,
    String? fullName,
    String? email,
    String? avatarUrl,
    String? profession,
    String? bio,
    bool? isOrganization,
    bool? organizationVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      profession: profession ?? this.profession,
      bio: bio ?? this.bio,
      isOrganization: isOrganization ?? this.isOrganization,
      organizationVerified: organizationVerified ?? this.organizationVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
