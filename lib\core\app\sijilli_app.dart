import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../constants/app_constants.dart';
import '../localization/localization_service.dart';
import '../di/injection_container.dart' as di;
import '../../presentation/providers/localization/localization_provider.dart';
import '../../presentation/providers/auth/auth_bloc.dart';
import '../../presentation/screens/splash/splash_screen.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/auth/forgot_password_screen.dart';
import '../../l10n/generated/app_localizations.dart';

/// Main application widget for Sijilli
class SijilliApp extends StatelessWidget {
  const SijilliApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(create: (context) => di.sl<AuthBloc>()),
      ],
      child: ChangeNotifierProvider(
        create: (context) => LocalizationProvider(LocalizationService.instance),
        child: Consumer<LocalizationProvider>(
          builder: (context, localizationProvider, child) {
            return MaterialApp(
              title: AppConstants.appName,
              debugShowCheckedModeBanner: false,

              // Theme configuration
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: ThemeMode.system,

              // Localization
              locale: localizationProvider.currentLocale,
              supportedLocales: localizationProvider.supportedLocales,
              localizationsDelegates: [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],

              // Navigation
              home: const SplashScreen(),
              routes: {
                LoginScreen.routeName: (context) => const LoginScreen(),
                RegisterScreen.routeName: (context) => const RegisterScreen(),
                ForgotPasswordScreen.routeName:
                    (context) => const ForgotPasswordScreen(),
              },

              // Global app configuration
              builder: (context, child) {
                return Directionality(
                  textDirection: localizationProvider.textDirection,
                  child: child ?? const SizedBox.shrink(),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
