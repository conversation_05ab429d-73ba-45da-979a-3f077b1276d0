import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/user_entity.dart';
import '../repositories/user_repository.dart';
import 'usecase.dart';

/// Use case for getting the current authenticated user
class GetCurrentUser implements UseCase<UserEntity, NoParams> {
  final UserRepository repository;

  const GetCurrentUser(this.repository);

  @override
  Future<Either<Failure, UserEntity>> call(NoParams params) async {
    return await repository.getCurrentUser();
  }
}
