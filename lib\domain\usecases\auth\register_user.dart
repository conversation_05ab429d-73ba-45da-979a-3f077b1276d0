import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/auth_entity.dart';
import '../../repositories/auth_repository.dart';
import '../usecase.dart';

/// Use case for user registration
class <PERSON>User implements UseCase<AuthEntity, RegisterUserParams> {
  final AuthRepository repository;

  const RegisterUser(this.repository);

  @override
  Future<Either<Failure, AuthEntity>> call(RegisterUserParams params) async {
    return await repository.signUpWithEmailAndPassword(
      email: params.email,
      password: params.password,
      fullName: params.fullName,
      username: params.username,
    );
  }
}

/// Parameters for register user use case
class RegisterUserParams {
  final String email;
  final String password;
  final String fullName;
  final String username;

  const RegisterUserParams({
    required this.email,
    required this.password,
    required this.fullName,
    required this.username,
  });
}
