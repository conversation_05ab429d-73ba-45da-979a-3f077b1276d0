import 'package:equatable/equatable.dart';

/// Base class for all failures in the application
abstract class Failure extends Equatable {
  final String message;
  final String? code;

  const Failure({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

/// Server failure - when there's an issue with the server
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

/// Network failure - when there's no internet connection
class NetworkFailure extends Failure {
  const NetworkFailure({
    super.message = 'خطأ في الاتصال بالشبكة',
    super.code = 'NETWORK_ERROR',
  });
}

/// Authentication failure - when authentication fails
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });
}

/// Validation failure - when input validation fails
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code = 'VALIDATION_ERROR',
  });
}

/// Cache failure - when there's an issue with local storage
class CacheFailure extends Failure {
  const CacheFailure({
    super.message = 'خطأ في التخزين المحلي',
    super.code = 'CACHE_ERROR',
  });
}

/// Permission failure - when required permissions are not granted
class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code = 'PERMISSION_ERROR',
  });
}

/// Not found failure - when requested resource is not found
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    super.message = 'العنصر المطلوب غير موجود',
    super.code = 'NOT_FOUND',
  });
}

/// Timeout failure - when request times out
class TimeoutFailure extends Failure {
  const TimeoutFailure({
    super.message = 'انتهت مهلة الاتصال',
    super.code = 'TIMEOUT_ERROR',
  });
}

/// Unknown failure - for unexpected errors
class UnknownFailure extends Failure {
  const UnknownFailure({
    super.message = 'حدث خطأ غير متوقع',
    super.code = 'UNKNOWN_ERROR',
  });
}

/// File failure - when there's an issue with file operations
class FileFailure extends Failure {
  const FileFailure({
    required super.message,
    super.code = 'FILE_ERROR',
  });
}

/// Database failure - when there's an issue with database operations
class DatabaseFailure extends Failure {
  const DatabaseFailure({
    required super.message,
    super.code = 'DATABASE_ERROR',
  });
}
