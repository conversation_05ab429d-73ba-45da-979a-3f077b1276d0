import 'package:equatable/equatable.dart';

/// Article entity representing an article in the system
class ArticleEntity extends Equatable {
  final String id;
  final String ownerId;
  final String title;
  final String content;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ArticleEntity({
    required this.id,
    required this.ownerId,
    required this.title,
    required this.content,
    this.isPublic = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a copy of this article with updated fields
  ArticleEntity copyWith({
    String? id,
    String? ownerId,
    String? title,
    String? content,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ArticleEntity(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      title: title ?? this.title,
      content: content ?? this.content,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get article excerpt (first 150 characters)
  String get excerpt {
    if (content.length <= 150) return content;
    return '${content.substring(0, 150)}...';
  }

  /// Get estimated reading time in minutes
  int get estimatedReadingTime {
    const wordsPerMinute = 200; // Average reading speed
    final wordCount = content.split(' ').length;
    final minutes = (wordCount / wordsPerMinute).ceil();
    return minutes < 1 ? 1 : minutes;
  }

  /// Check if article was recently created (within last 24 hours)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  /// Check if article was recently updated (within last hour)
  bool get isRecentlyUpdated {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    return difference.inHours < 1 && updatedAt.isAfter(createdAt);
  }

  /// Get content word count
  int get wordCount => content.split(' ').where((word) => word.isNotEmpty).length;

  /// Get content character count
  int get characterCount => content.length;

  @override
  List<Object?> get props => [
        id,
        ownerId,
        title,
        content,
        isPublic,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ArticleEntity(id: $id, title: $title, isPublic: $isPublic)';
  }
}
