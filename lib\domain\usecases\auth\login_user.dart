import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../entities/auth_entity.dart';
import '../../repositories/auth_repository.dart';
import '../usecase.dart';

/// Use case for user login
class LoginUser implements UseCase<AuthEntity, LoginUserParams> {
  final AuthRepository repository;

  const LoginUser(this.repository);

  @override
  Future<Either<Failure, AuthEntity>> call(LoginUserParams params) async {
    return await repository.signInWithEmailAndPassword(
      email: params.email,
      password: params.password,
    );
  }
}

/// Parameters for login user use case
class LoginUserParams {
  final String email;
  final String password;

  const LoginUserParams({
    required this.email,
    required this.password,
  });
}
