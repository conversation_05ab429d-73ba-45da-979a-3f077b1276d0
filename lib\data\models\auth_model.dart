import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/auth_entity.dart';

/// Authentication model for data layer
class AuthModel extends AuthEntity {
  const AuthModel({
    super.accessToken,
    super.refreshToken,
    super.expiresAt,
    super.userId,
    super.email,
    super.isAuthenticated = false,
  });

  /// Create AuthModel from Supabase Session
  factory AuthModel.fromSession(Session session) {
    return AuthModel(
      accessToken: session.accessToken,
      refreshToken: session.refreshToken,
      expiresAt: DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000),
      userId: session.user.id,
      email: session.user.email,
      isAuthenticated: true,
    );
  }

  /// Create AuthModel from Supabase User (for cases where session might be null)
  factory AuthModel.fromUser(User user) {
    return AuthModel(
      userId: user.id,
      email: user.email,
      isAuthenticated: true,
    );
  }

  /// Create unauthenticated AuthModel
  factory AuthModel.unauthenticated() {
    return const AuthModel();
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt?.millisecondsSinceEpoch,
      'user_id': userId,
      'email': email,
      'is_authenticated': isAuthenticated,
    };
  }

  /// Create from JSON
  factory AuthModel.fromJson(Map<String, dynamic> json) {
    return AuthModel(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      expiresAt: json['expires_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['expires_at'])
          : null,
      userId: json['user_id'],
      email: json['email'],
      isAuthenticated: json['is_authenticated'] ?? false,
    );
  }

  /// Copy with new values
  @override
  AuthModel copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    String? userId,
    String? email,
    bool? isAuthenticated,
  }) {
    return AuthModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Login request model
class LoginRequestModel {
  final String email;
  final String password;

  const LoginRequestModel({
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

/// Registration request model
class RegisterRequestModel {
  final String email;
  final String password;
  final String fullName;
  final String username;

  const RegisterRequestModel({
    required this.email,
    required this.password,
    required this.fullName,
    required this.username,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'data': {
        'full_name': fullName,
        'username': username,
      },
    };
  }
}

/// Password reset request model
class PasswordResetRequestModel {
  final String email;
  final String? redirectTo;

  const PasswordResetRequestModel({
    required this.email,
    this.redirectTo,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      if (redirectTo != null) 'redirect_to': redirectTo,
    };
  }
}
