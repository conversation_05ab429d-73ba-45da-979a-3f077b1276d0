/// Generated file. Do not edit.
///
/// Localization for Sijilli App
/// Arabic (ar) and English (en) support


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Sijilli';

  @override
  String get appDescription => 'Appointment and Article Management App';

  @override
  String get welcome => 'Welcome';

  @override
  String get welcomeToSijilli => 'Welcome to <PERSON><PERSON><PERSON>';

  @override
  String get appUnderDevelopment => 'App under development';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get create => 'Create';

  @override
  String get update => 'Update';

  @override
  String get confirm => 'Confirm';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get done => 'Done';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get back => 'Back';

  @override
  String get close => 'Close';

  @override
  String get search => 'Search';

  @override
  String get searchHint => 'Search...';

  @override
  String get noResults => 'No results found';

  @override
  String get emptyList => 'No items to display';

  @override
  String get networkError => 'Network connection error';

  @override
  String get unknownError => 'An unexpected error occurred';

  @override
  String get validationError => 'Please check the entered data';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get logout => 'Logout';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get username => 'Username';

  @override
  String get fullName => 'Full Name';

  @override
  String get signIn => 'Sign In';

  @override
  String get signUp => 'Sign Up';

  @override
  String get signOut => 'Sign Out';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get changePassword => 'Change Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get emailHint => 'Enter your email';

  @override
  String get passwordHint => 'Enter your password';

  @override
  String get confirmPasswordHint => 'Re-enter your password';

  @override
  String get usernameHint => 'Enter your username';

  @override
  String get fullNameHint => 'Enter your full name';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get usernameRequired => 'Username is required';

  @override
  String get fullNameRequired => 'Full name is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get usernameInvalid => 'Username must be 3-24 characters with letters and numbers only';

  @override
  String get signInTitle => 'Welcome Back';

  @override
  String get signInSubtitle => 'Sign in to continue';

  @override
  String get signUpTitle => 'Create New Account';

  @override
  String get signUpSubtitle => 'Join the Sijilli community';

  @override
  String get forgotPasswordTitle => 'Forgot Password';

  @override
  String get forgotPasswordSubtitle => 'Enter your email to reset your password';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get backToSignIn => 'Back to Sign In';

  @override
  String get signInWithGoogle => 'Sign in with Google';

  @override
  String get signInWithApple => 'Sign in with Apple';

  @override
  String get signInWithFacebook => 'Sign in with Facebook';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get resetLinkSent => 'Password reset link sent to your email';

  @override
  String get checkYourEmail => 'Check your email';

  @override
  String get emailConfirmationSent => 'Email confirmation link sent to your email';

  @override
  String get resendConfirmation => 'Resend Confirmation';

  @override
  String get signInSuccess => 'Signed in successfully';

  @override
  String get signUpSuccess => 'Account created successfully';

  @override
  String get signOutSuccess => 'Signed out successfully';

  @override
  String get passwordResetSuccess => 'Password reset link sent';

  @override
  String get passwordUpdateSuccess => 'Password updated successfully';

  @override
  String get authError => 'Authentication error';

  @override
  String get invalidCredentials => 'Invalid credentials';

  @override
  String get userNotFound => 'User not found';

  @override
  String get emailAlreadyExists => 'Email already in use';

  @override
  String get usernameAlreadyExists => 'Username already in use';

  @override
  String get weakPassword => 'Password is too weak';

  @override
  String get tooManyRequests => 'Too many requests, please try again later';

  @override
  String get sessionExpired => 'Session expired, please sign in again';

  @override
  String get phone => 'Phone Number';

  @override
  String get bio => 'Bio';

  @override
  String get profession => 'Profession';

  @override
  String get profile => 'Profile';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get appointments => 'Appointments';

  @override
  String get createAppointment => 'Create Appointment';

  @override
  String get editAppointment => 'Edit Appointment';

  @override
  String get appointmentDetails => 'Appointment Details';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get startTime => 'Start Time';

  @override
  String get endTime => 'End Time';

  @override
  String get location => 'Location';

  @override
  String get region => 'Region';

  @override
  String get building => 'Building';

  @override
  String get allDay => 'All Day';

  @override
  String get isPublic => 'Public';

  @override
  String get articles => 'Articles';

  @override
  String get createArticle => 'Create Article';

  @override
  String get editArticle => 'Edit Article';

  @override
  String get articleDetails => 'Article Details';

  @override
  String get content => 'Content';

  @override
  String get notifications => 'Notifications';

  @override
  String get noNotifications => 'No notifications';

  @override
  String get markAsRead => 'Mark as Read';

  @override
  String get markAllAsRead => 'Mark All as Read';

  @override
  String get followers => 'Followers';

  @override
  String get following => 'Following';

  @override
  String get follow => 'Follow';

  @override
  String get unfollow => 'Unfollow';

  @override
  String get today => 'Today';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get hijriDate => 'Hijri Date';

  @override
  String get gregorianDate => 'Gregorian Date';

  @override
  String get hijriCorrection => 'Hijri Correction';

  @override
  String get organization => 'Organization';

  @override
  String get verified => 'Verified';

  @override
  String get repost => 'Repost';

  @override
  String get share => 'Share';

  @override
  String get link => 'Link';

  @override
  String get url => 'URL';

  @override
  String get website => 'Website';

  @override
  String get socialLinks => 'Social Links';

  @override
  String get avatar => 'Avatar';

  @override
  String get banner => 'Banner';

  @override
  String get uploadImage => 'Upload Image';

  @override
  String get changeImage => 'Change Image';

  @override
  String get removeImage => 'Remove Image';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get selectImageSource => 'Select Image Source';

  @override
  String get permissionDenied => 'Permission denied';

  @override
  String get cameraPermissionRequired => 'Camera permission is required';

  @override
  String get storagePermissionRequired => 'Storage permission is required';

  @override
  String get deleteConfirmation => 'Are you sure you want to delete this item?';

  @override
  String get logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get unsavedChanges => 'You have unsaved changes. Do you want to discard them?';

  @override
  String get discard => 'Discard';

  @override
  String get keepEditing => 'Keep Editing';
}
