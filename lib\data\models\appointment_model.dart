import '../../domain/entities/appointment_entity.dart';

/// Appointment model for data layer
class AppointmentModel extends AppointmentEntity {
  const AppointmentModel({
    required super.id,
    required super.ownerId,
    required super.title,
    super.description,
    super.link,
    required super.region,
    required super.building,
    required super.dateGregorian,
    super.dateHijri,
    super.timeStart,
    super.timeEnd,
    super.allDay = false,
    super.isPublic = false,
    super.sunSetTime,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create AppointmentModel from JSON
  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      id: json['id'] as String,
      ownerId: json['owner_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      link: json['link'] as String?,
      region: json['region'] as String,
      building: json['building'] as String,
      dateGregorian: DateTime.parse(json['date_gregorian'] as String),
      dateHijri: json['date_hijri'] != null 
          ? DateTime.parse(json['date_hijri'] as String) 
          : null,
      timeStart: json['time_start'] != null 
          ? _parseTime(json['time_start'] as String) 
          : null,
      timeEnd: json['time_end'] != null 
          ? _parseTime(json['time_end'] as String) 
          : null,
      allDay: json['all_day'] as bool? ?? false,
      isPublic: json['is_public'] as bool? ?? false,
      sunSetTime: json['sun_set_time'] != null 
          ? _parseTime(json['sun_set_time'] as String) 
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// Convert AppointmentModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner_id': ownerId,
      'title': title,
      'description': description,
      'link': link,
      'region': region,
      'building': building,
      'date_gregorian': dateGregorian.toIso8601String().split('T')[0],
      'date_hijri': dateHijri?.toIso8601String().split('T')[0],
      'time_start': timeStart != null ? _formatTime(timeStart!) : null,
      'time_end': timeEnd != null ? _formatTime(timeEnd!) : null,
      'all_day': allDay,
      'is_public': isPublic,
      'sun_set_time': sunSetTime != null ? _formatTime(sunSetTime!) : null,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create AppointmentModel from AppointmentEntity
  factory AppointmentModel.fromEntity(AppointmentEntity entity) {
    return AppointmentModel(
      id: entity.id,
      ownerId: entity.ownerId,
      title: entity.title,
      description: entity.description,
      link: entity.link,
      region: entity.region,
      building: entity.building,
      dateGregorian: entity.dateGregorian,
      dateHijri: entity.dateHijri,
      timeStart: entity.timeStart,
      timeEnd: entity.timeEnd,
      allDay: entity.allDay,
      isPublic: entity.isPublic,
      sunSetTime: entity.sunSetTime,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Convert to AppointmentEntity
  AppointmentEntity toEntity() {
    return AppointmentEntity(
      id: id,
      ownerId: ownerId,
      title: title,
      description: description,
      link: link,
      region: region,
      building: building,
      dateGregorian: dateGregorian,
      dateHijri: dateHijri,
      timeStart: timeStart,
      timeEnd: timeEnd,
      allDay: allDay,
      isPublic: isPublic,
      sunSetTime: sunSetTime,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Parse time string (HH:MM:SS) to DateTime
  static DateTime? _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      final now = DateTime.now();
      return DateTime(
        now.year,
        now.month,
        now.day,
        int.parse(parts[0]),
        int.parse(parts[1]),
        parts.length > 2 ? int.parse(parts[2]) : 0,
      );
    } catch (e) {
      return null;
    }
  }

  /// Format DateTime to time string (HH:MM:SS)
  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// Create a copy with updated fields
  @override
  AppointmentModel copyWith({
    String? id,
    String? ownerId,
    String? title,
    String? description,
    String? link,
    String? region,
    String? building,
    DateTime? dateGregorian,
    DateTime? dateHijri,
    DateTime? timeStart,
    DateTime? timeEnd,
    bool? allDay,
    bool? isPublic,
    DateTime? sunSetTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      title: title ?? this.title,
      description: description ?? this.description,
      link: link ?? this.link,
      region: region ?? this.region,
      building: building ?? this.building,
      dateGregorian: dateGregorian ?? this.dateGregorian,
      dateHijri: dateHijri ?? this.dateHijri,
      timeStart: timeStart ?? this.timeStart,
      timeEnd: timeEnd ?? this.timeEnd,
      allDay: allDay ?? this.allDay,
      isPublic: isPublic ?? this.isPublic,
      sunSetTime: sunSetTime ?? this.sunSetTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
