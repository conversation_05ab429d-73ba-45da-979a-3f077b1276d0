import 'package:equatable/equatable.dart';
import '../../../domain/entities/auth_entity.dart';
import '../../../domain/entities/user_entity.dart';

/// Base class for authentication states
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// Loading state
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// Authenticated state
class AuthAuthenticated extends AuthState {
  final AuthEntity authEntity;
  final UserEntity? user;

  const AuthAuthenticated({
    required this.authEntity,
    this.user,
  });

  @override
  List<Object?> get props => [authEntity, user];
}

/// Unauthenticated state
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// Error state
class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// Sign up success state (when email confirmation is required)
class AuthSignUpSuccess extends AuthState {
  final String email;
  final String message;

  const AuthSignUpSuccess({
    required this.email,
    required this.message,
  });

  @override
  List<Object> get props => [email, message];
}

/// Magic link sent state
class AuthMagicLinkSent extends AuthState {
  final String email;
  final String message;

  const AuthMagicLinkSent({
    required this.email,
    required this.message,
  });

  @override
  List<Object> get props => [email, message];
}

/// Password reset email sent state
class AuthPasswordResetSent extends AuthState {
  final String email;
  final String message;

  const AuthPasswordResetSent({
    required this.email,
    required this.message,
  });

  @override
  List<Object> get props => [email, message];
}

/// Password updated successfully state
class AuthPasswordUpdated extends AuthState {
  final String message;

  const AuthPasswordUpdated({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

/// Email confirmation resent state
class AuthEmailConfirmationResent extends AuthState {
  final String email;
  final String message;

  const AuthEmailConfirmationResent({
    required this.email,
    required this.message,
  });

  @override
  List<Object> get props => [email, message];
}

/// Account deleted state
class AuthAccountDeleted extends AuthState {
  final String message;

  const AuthAccountDeleted({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

/// OTP verification required state
class AuthOTPRequired extends AuthState {
  final String email;
  final String type;
  final String message;

  const AuthOTPRequired({
    required this.email,
    required this.type,
    required this.message,
  });

  @override
  List<Object> get props => [email, type, message];
}

/// Session expired state
class AuthSessionExpired extends AuthState {
  final String message;

  const AuthSessionExpired({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}
