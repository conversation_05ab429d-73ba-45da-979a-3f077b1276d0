import 'package:equatable/equatable.dart';

/// Appointment entity representing an appointment in the system
class AppointmentEntity extends Equatable {
  final String id;
  final String ownerId;
  final String title;
  final String? description;
  final String? link;
  final String region;
  final String building;
  final DateTime dateGregorian;
  final DateTime? dateHijri;
  final DateTime? timeStart;
  final DateTime? timeEnd;
  final bool allDay;
  final bool isPublic;
  final DateTime? sunSetTime;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppointmentEntity({
    required this.id,
    required this.ownerId,
    required this.title,
    this.description,
    this.link,
    required this.region,
    required this.building,
    required this.dateGregorian,
    this.dateHijri,
    this.timeStart,
    this.timeEnd,
    this.allDay = false,
    this.isPublic = false,
    this.sunSetTime,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a copy of this appointment with updated fields
  AppointmentEntity copyWith({
    String? id,
    String? ownerId,
    String? title,
    String? description,
    String? link,
    String? region,
    String? building,
    DateTime? dateGregorian,
    DateTime? dateHijri,
    DateTime? timeStart,
    DateTime? timeEnd,
    bool? allDay,
    bool? isPublic,
    DateTime? sunSetTime,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppointmentEntity(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      title: title ?? this.title,
      description: description ?? this.description,
      link: link ?? this.link,
      region: region ?? this.region,
      building: building ?? this.building,
      dateGregorian: dateGregorian ?? this.dateGregorian,
      dateHijri: dateHijri ?? this.dateHijri,
      timeStart: timeStart ?? this.timeStart,
      timeEnd: timeEnd ?? this.timeEnd,
      allDay: allDay ?? this.allDay,
      isPublic: isPublic ?? this.isPublic,
      sunSetTime: sunSetTime ?? this.sunSetTime,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get full location string
  String get fullLocation => '$region, $building';

  /// Check if appointment is today
  bool get isToday {
    final now = DateTime.now();
    return dateGregorian.year == now.year &&
           dateGregorian.month == now.month &&
           dateGregorian.day == now.day;
  }

  /// Check if appointment is in the past
  bool get isPast {
    final now = DateTime.now();
    if (allDay) {
      return dateGregorian.isBefore(DateTime(now.year, now.month, now.day));
    }
    return timeStart?.isBefore(now) ?? dateGregorian.isBefore(now);
  }

  /// Check if appointment is upcoming
  bool get isUpcoming => !isPast;

  /// Get duration in minutes (if both start and end times are set)
  int? get durationInMinutes {
    if (timeStart != null && timeEnd != null) {
      return timeEnd!.difference(timeStart!).inMinutes;
    }
    return null;
  }

  /// Get formatted time range
  String? get timeRange {
    if (allDay) return 'طوال اليوم';
    if (timeStart == null) return null;
    
    final startTime = '${timeStart!.hour.toString().padLeft(2, '0')}:${timeStart!.minute.toString().padLeft(2, '0')}';
    if (timeEnd == null) return startTime;
    
    final endTime = '${timeEnd!.hour.toString().padLeft(2, '0')}:${timeEnd!.minute.toString().padLeft(2, '0')}';
    return '$startTime - $endTime';
  }

  @override
  List<Object?> get props => [
        id,
        ownerId,
        title,
        description,
        link,
        region,
        building,
        dateGregorian,
        dateHijri,
        timeStart,
        timeEnd,
        allDay,
        isPublic,
        sunSetTime,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'AppointmentEntity(id: $id, title: $title, date: $dateGregorian, location: $fullLocation)';
  }
}
