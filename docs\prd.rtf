{\rtf1\adeflang1025\ansi\ansicpg1256\uc1\adeff31507\deff0\stshfdbch31505\stshfloch31506\stshfhich31506\stshfbi31507\deflang1033\deflangfe1033\themelang1033\themelangfe0\themelangcs0{\fonttbl{\f0\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\f1\fbidi \fswiss\fcharset0\fprq2{\*\panose 020b0604020202020204}Arial;}
{\f2\fbidi \fmodern\fcharset0\fprq1{\*\panose 02070309020205020404}Courier New;}{\f3\fbidi \froman\fcharset2\fprq2{\*\panose 05050102010706020507}Symbol;}{\f10\fbidi \fnil\fcharset2\fprq2{\*\panose 05000000000000000000}Wingdings;}
{\f34\fbidi \froman\fcharset0\fprq2{\*\panose 02040503050406030204}Cambria Math;}{\f43\fbidi \fswiss\fcharset0\fprq2 Aptos;}{\f45\fbidi \fswiss\fcharset0\fprq2{\*\panose 00000000000000000000}Segoe UI Emoji;}
{\flomajor\f31500\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\fdbmajor\f31501\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\fhimajor\f31502\fbidi \fswiss\fcharset0\fprq2 Aptos Display;}
{\fbimajor\f31503\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\flominor\f31504\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}
{\fdbminor\f31505\fbidi \froman\fcharset0\fprq2{\*\panose 02020603050405020304}Times New Roman;}{\fhiminor\f31506\fbidi \fswiss\fcharset0\fprq2 Aptos;}{\fbiminor\f31507\fbidi \fswiss\fcharset0\fprq2{\*\panose 020b0604020202020204}Arial;}
{\f47\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}{\f48\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}{\f50\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}{\f51\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}
{\f52\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\f53\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\f54\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}
{\f55\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\f57\fbidi \fswiss\fcharset238\fprq2 Arial CE;}{\f58\fbidi \fswiss\fcharset204\fprq2 Arial Cyr;}{\f60\fbidi \fswiss\fcharset161\fprq2 Arial Greek;}
{\f61\fbidi \fswiss\fcharset162\fprq2 Arial Tur;}{\f62\fbidi \fswiss\fcharset177\fprq2 Arial (Hebrew);}{\f63\fbidi \fswiss\fcharset178\fprq2 Arial (Arabic);}{\f64\fbidi \fswiss\fcharset186\fprq2 Arial Baltic;}
{\f65\fbidi \fswiss\fcharset163\fprq2 Arial (Vietnamese);}{\f67\fbidi \fmodern\fcharset238\fprq1 Courier New CE;}{\f68\fbidi \fmodern\fcharset204\fprq1 Courier New Cyr;}{\f70\fbidi \fmodern\fcharset161\fprq1 Courier New Greek;}
{\f71\fbidi \fmodern\fcharset162\fprq1 Courier New Tur;}{\f72\fbidi \fmodern\fcharset177\fprq1 Courier New (Hebrew);}{\f73\fbidi \fmodern\fcharset178\fprq1 Courier New (Arabic);}{\f74\fbidi \fmodern\fcharset186\fprq1 Courier New Baltic;}
{\f75\fbidi \fmodern\fcharset163\fprq1 Courier New (Vietnamese);}{\f387\fbidi \froman\fcharset238\fprq2 Cambria Math CE;}{\f388\fbidi \froman\fcharset204\fprq2 Cambria Math Cyr;}{\f390\fbidi \froman\fcharset161\fprq2 Cambria Math Greek;}
{\f391\fbidi \froman\fcharset162\fprq2 Cambria Math Tur;}{\f394\fbidi \froman\fcharset186\fprq2 Cambria Math Baltic;}{\f395\fbidi \froman\fcharset163\fprq2 Cambria Math (Vietnamese);}{\f477\fbidi \fswiss\fcharset238\fprq2 Aptos CE;}
{\f478\fbidi \fswiss\fcharset204\fprq2 Aptos Cyr;}{\f480\fbidi \fswiss\fcharset161\fprq2 Aptos Greek;}{\f481\fbidi \fswiss\fcharset162\fprq2 Aptos Tur;}{\f484\fbidi \fswiss\fcharset186\fprq2 Aptos Baltic;}
{\f485\fbidi \fswiss\fcharset163\fprq2 Aptos (Vietnamese);}{\flomajor\f31508\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}{\flomajor\f31509\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}
{\flomajor\f31511\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}{\flomajor\f31512\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}{\flomajor\f31513\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}
{\flomajor\f31514\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\flomajor\f31515\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}{\flomajor\f31516\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}
{\fdbmajor\f31518\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}{\fdbmajor\f31519\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}{\fdbmajor\f31521\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}
{\fdbmajor\f31522\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}{\fdbmajor\f31523\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\fdbmajor\f31524\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}
{\fdbmajor\f31525\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}{\fdbmajor\f31526\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\fhimajor\f31528\fbidi \fswiss\fcharset238\fprq2 Aptos Display CE;}
{\fhimajor\f31529\fbidi \fswiss\fcharset204\fprq2 Aptos Display Cyr;}{\fhimajor\f31531\fbidi \fswiss\fcharset161\fprq2 Aptos Display Greek;}{\fhimajor\f31532\fbidi \fswiss\fcharset162\fprq2 Aptos Display Tur;}
{\fhimajor\f31535\fbidi \fswiss\fcharset186\fprq2 Aptos Display Baltic;}{\fhimajor\f31536\fbidi \fswiss\fcharset163\fprq2 Aptos Display (Vietnamese);}{\fbimajor\f31538\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}
{\fbimajor\f31539\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}{\fbimajor\f31541\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}{\fbimajor\f31542\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}
{\fbimajor\f31543\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\fbimajor\f31544\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\fbimajor\f31545\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}
{\fbimajor\f31546\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\flominor\f31548\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}{\flominor\f31549\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}
{\flominor\f31551\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}{\flominor\f31552\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}{\flominor\f31553\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}
{\flominor\f31554\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}{\flominor\f31555\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}{\flominor\f31556\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}
{\fdbminor\f31558\fbidi \froman\fcharset238\fprq2 Times New Roman CE;}{\fdbminor\f31559\fbidi \froman\fcharset204\fprq2 Times New Roman Cyr;}{\fdbminor\f31561\fbidi \froman\fcharset161\fprq2 Times New Roman Greek;}
{\fdbminor\f31562\fbidi \froman\fcharset162\fprq2 Times New Roman Tur;}{\fdbminor\f31563\fbidi \froman\fcharset177\fprq2 Times New Roman (Hebrew);}{\fdbminor\f31564\fbidi \froman\fcharset178\fprq2 Times New Roman (Arabic);}
{\fdbminor\f31565\fbidi \froman\fcharset186\fprq2 Times New Roman Baltic;}{\fdbminor\f31566\fbidi \froman\fcharset163\fprq2 Times New Roman (Vietnamese);}{\fhiminor\f31568\fbidi \fswiss\fcharset238\fprq2 Aptos CE;}
{\fhiminor\f31569\fbidi \fswiss\fcharset204\fprq2 Aptos Cyr;}{\fhiminor\f31571\fbidi \fswiss\fcharset161\fprq2 Aptos Greek;}{\fhiminor\f31572\fbidi \fswiss\fcharset162\fprq2 Aptos Tur;}{\fhiminor\f31575\fbidi \fswiss\fcharset186\fprq2 Aptos Baltic;}
{\fhiminor\f31576\fbidi \fswiss\fcharset163\fprq2 Aptos (Vietnamese);}{\fbiminor\f31578\fbidi \fswiss\fcharset238\fprq2 Arial CE;}{\fbiminor\f31579\fbidi \fswiss\fcharset204\fprq2 Arial Cyr;}{\fbiminor\f31581\fbidi \fswiss\fcharset161\fprq2 Arial Greek;}
{\fbiminor\f31582\fbidi \fswiss\fcharset162\fprq2 Arial Tur;}{\fbiminor\f31583\fbidi \fswiss\fcharset177\fprq2 Arial (Hebrew);}{\fbiminor\f31584\fbidi \fswiss\fcharset178\fprq2 Arial (Arabic);}
{\fbiminor\f31585\fbidi \fswiss\fcharset186\fprq2 Arial Baltic;}{\fbiminor\f31586\fbidi \fswiss\fcharset163\fprq2 Arial (Vietnamese);}}{\colortbl;\red0\green0\blue0;\red0\green0\blue255;\red0\green255\blue255;\red0\green255\blue0;\red255\green0\blue255;
\red255\green0\blue0;\red255\green255\blue0;\red255\green255\blue255;\red0\green0\blue128;\red0\green128\blue128;\red0\green128\blue0;\red128\green0\blue128;\red128\green0\blue0;\red128\green128\blue0;\red128\green128\blue128;\red192\green192\blue192;
\red0\green0\blue0;\red0\green0\blue0;}{\*\defchp \fs24\kerning2\loch\af31506\hich\af31506\dbch\af31505 }{\*\defpap \ql \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 }\noqfpromote {\stylesheet{
\rtlpar \qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af31507\afs24\alang1025 \ltrch\fcs0 
\fs24\lang1033\langfe1033\kerning2\loch\f31506\hich\af31506\dbch\af31505\cgrid\langnp1033\langfenp1033 \snext0 \sqformat \spriority0 Normal;}{\*\cs10 \additive \sunhideused \spriority1 Default Paragraph Font;}{\*
\ts11\tsrowd\trftsWidthB3\trpaddl108\trpaddr108\trpaddfl3\trpaddft3\trpaddfb3\trpaddfr3\trcbpat1\trcfpat1\tblind0\tblindtype3\tsvertalt\tsbrdrt\tsbrdrl\tsbrdrb\tsbrdrr\tsbrdrdgl\tsbrdrdgr\tsbrdrh\tsbrdrv \ql \li0\ri0\sa160\sl278\slmult1
\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 \rtlch\fcs1 \af31507\afs24\alang1025 \ltrch\fcs0 \fs24\lang1033\langfe1033\kerning2\loch\f31506\hich\af31506\dbch\af31505\cgrid\langnp1033\langfenp1033 
\snext11 \ssemihidden \sunhideused Normal Table;}}{\*\listtable{\list\listtemplateid905744474{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'02\'00.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }
{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'02.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc0
\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'03.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li2880\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc0\levelnfcn0\leveljc2
\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'04.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0
\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'05.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li4320\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative
\levelspace0\levelindent0{\leveltext\'02\'06.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5040\jclisttab\tx5040\lin5040 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0
{\leveltext\'02\'07.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5760\jclisttab\tx5760\lin5760 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\'02\'08.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname ;}\listid126172004}{\list\listtemplateid-1895793166{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0
\levelindent0{\leveltext\'02\'00.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext
\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 
\fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'03.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li2880
\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'04.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li3600\jclisttab\tx3600\lin3600 }
{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'05.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li4320\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc0
\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'06.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5040\jclisttab\tx5040\lin5040 }{\listlevel\levelnfc0\levelnfcn0\leveljc2
\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'07.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5760\jclisttab\tx5760\lin5760 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0
\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'08.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname ;}\listid903612330}{\list\listtemplateid336119606{\listlevel\levelnfc23
\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fs20\fbias0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0
\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0
\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2880\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}
\f10\fs20\fbias0 \fi-360\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li4320
\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5040\jclisttab\tx5040\lin5040 }
{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5760\jclisttab\tx5760\lin5760 }{\listlevel\levelnfc23\levelnfcn23
\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname ;}\listid1105811595}{\list\listtemplateid276704632
{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fs20\fbias0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0
\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0
\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2880\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}
\f10\fs20\fbias0 \fi-360\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li4320
\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5040\jclisttab\tx5040\lin5040 }
{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5760\jclisttab\tx5760\lin5760 }{\listlevel\levelnfc23\levelnfcn23
\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname ;}\listid1141732566}{\list\listtemplateid-499246720
{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01\u-3913 ?;}{\levelnumbers;}\f3\fs20\fbias0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0
\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative
\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2880\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}
\f10\fs20\fbias0 \fi-360\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li4320
\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5040\jclisttab\tx5040\lin5040 }
{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li5760\jclisttab\tx5760\lin5760 }{\listlevel\levelnfc23\levelnfcn23
\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname ;}\listid1327901234}{\list\listtemplateid1838810630
{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'02\'00.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li720\jclisttab\tx720\lin720 }{\listlevel\levelnfc23\levelnfcn23\leveljc2
\leveljcn0\levelfollow0\levelstartat1\levelspace0\levelindent0{\leveltext\'01o;}{\levelnumbers;}\f2\fs20\fbias0 \fi-360\li1440\jclisttab\tx1440\lin1440 }{\listlevel\levelnfc23\levelnfcn23\leveljc2\leveljcn0\levelfollow0\levelstartat1\levelspace0
\levelindent0{\leveltext\'01\u-3929 ?;}{\levelnumbers;}\f10\fs20\fbias0 \fi-360\li2160\jclisttab\tx2160\lin2160 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext
\'02\'03.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li2880\jclisttab\tx2880\lin2880 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'04.;}{\levelnumbers
\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li3600\jclisttab\tx3600\lin3600 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'05.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 
\ltrch\fcs0 \fi-360\li4320\jclisttab\tx4320\lin4320 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'06.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5040
\jclisttab\tx5040\lin5040 }{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'07.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li5760\jclisttab\tx5760\lin5760 }
{\listlevel\levelnfc0\levelnfcn0\leveljc2\leveljcn0\levelfollow0\levelstartat1\lvltentative\levelspace0\levelindent0{\leveltext\'02\'08.;}{\levelnumbers\'01;}\rtlch\fcs1 \af0 \ltrch\fcs0 \fi-360\li6480\jclisttab\tx6480\lin6480 }{\listname 
;}\listid1614240871}}{\*\listoverridetable{\listoverride\listid1105811595\listoverridecount0\ls1}{\listoverride\listid126172004\listoverridecount0\ls2}{\listoverride\listid903612330\listoverridecount0\ls3}{\listoverride\listid1614240871
\listoverridecount0\ls4}{\listoverride\listid1141732566\listoverridecount0\ls5}{\listoverride\listid1327901234\listoverridecount0\ls6}}{\*\pgptbl {\pgp\ipgp0\itap0\li0\ri0\sb0\sa0}{\pgp\ipgp0\itap0\li0\ri0\sb0\sa0}}{\*\rsidtbl \rsid5584471\rsid6883285
\rsid9445006}{\mmathPr\mmathFont34\mbrkBin0\mbrkBinSub0\msmallFrac0\mdispDef1\mlMargin0\mrMargin0\mdefJc1\mwrapIndent1440\mintLim0\mnaryLim1}{\info{\operator Hawraa A.Husain Ahmed Malalla}{\creatim\yr2025\mo5\dy30\hr17\min3}
{\revtim\yr2025\mo5\dy30\hr17\min3}{\version2}{\edmins0}{\nofpages6}{\nofwords1282}{\nofchars7311}{\nofcharsws8576}{\vern119}}{\*\xmlnstbl {\xmlns1 http://schemas.microsoft.com/office/word/2003/wordml}}
\paperw12240\paperh15840\margl1800\margr1800\margt1440\margb1440\gutter0\ltrsect 
\widowctrl\ftnbj\aenddoc\trackmoves0\trackformatting1\donotembedsysfont0\relyonvml0\donotembedlingdata1\grfdocevents0\validatexml0\showplaceholdtext0\ignoremixedcontent0\saveinvalidxml0\showxmlerrors0\horzdoc\dghspace120\dgvspace120\dghorigin1701
\dgvorigin1984\dghshow0\dgvshow3\jcompress\viewkind1\viewscale140\rsidroot5584471 \fet0{\*\wgrffmtfilter 2450}\ilfomacatclnup0\rtlpar \sectd \ltrsect\linex0\sectdefaultcl\sftnbj {\*\pnseclvl1\pnucrm\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}
{\*\pnseclvl2\pnucltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl3\pndec\pnqc\pnstart1\pnindent720\pnhang {\pntxta .}}{\*\pnseclvl4\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxta )}}{\*\pnseclvl5\pndec\pnqc\pnstart1\pnindent720\pnhang 
{\pntxtb (}{\pntxta )}}{\*\pnseclvl6\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl7\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}{\*\pnseclvl8\pnlcltr\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}
{\pntxta )}}{\*\pnseclvl9\pnlcrm\pnqc\pnstart1\pnindent720\pnhang {\pntxtb (}{\pntxta )}}\pard\plain \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 \rtlch\fcs1 
\af31507\afs24\alang1025 \ltrch\fcs0 \fs24\lang1033\langfe1033\kerning2\loch\af31506\hich\af31506\dbch\af31505\cgrid\langnp1033\langfenp1033 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Okay, building the "Sijilli" app with Flutter and Supabase is a solid choice! This combination allows for rapid development, a reactive user experience, and a scalable backend.
\par \hich\af31506\dbch\af31505\loch\f31506 Here's a breakdown of best practices and technologies to consider, drawing from your detailed project requirements:
\par }{\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 \u-10180\'3f\u-8417\'3f}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506  Core Philosophy
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls1\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Simplicity and Intuitive Actions:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  \hich\af31506\dbch\af31505\loch\f31506 
Keep this as a guiding principle from your PRD. Every feature and UI element should feel natural to the user. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Modern and Serious Design:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Adhere to contemporary social media UI/UX patterns while maintaining a calm and professional aesthetic. 
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31569\insrsid5584471\charrsid5584471 
\loch\af31569\dbch\af31505\hich\f31569 \'e0\'f0\'f5\'e8\'f2\'e5\'ea\'f2\'f3\'f0\'e0\loch\f31569  (App Architecture) and General Best Practices}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 1.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Modular Design:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Break down your app into distinct features/modules (e.g., Auth, Appointments, Profiles, Notifications, Articles, Settings). This makes the codebase easier to manage, test, and scale.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 2.\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Clean Code & Conventions:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls2\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Adopt a strict linting rule set (e.g., \hich\af31506\dbch\af31505\loch\f31506 flutter_lints\hich\af31506\dbch\af31505\loch\f31506  or \hich\af31506\dbch\af31505\loch\f31506 very_good_analysis\hich\af31506\dbch\af31505\loch\f31506 ).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 Follow Dart and Flutter style guides for consistency.

\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
Use meaningful names for variables, functions, and classes.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 3.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 State Management (Flutter):}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  \hich\af31506\dbch\af31505\loch\f31506 This is crucial for a reactive app like Sijilli. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls2\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Riverpod or Provider:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Both are excellent choices. Riverpod is often favored for its compile-time safety and testability, and it integrates well with asynchronous operations common with Supabase.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 4.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Version Control:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Use Git from the very beginning. Employ a branching strategy (e.g., Gitflow or GitHub Flow).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 5.\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Testing:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls2\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Unit Tests:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  For business logic (e.g., date conflict detection, permission checks).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Widget Tests:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  For UI components in isolation.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Integration Tests:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  To test complete features or user flows.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 6.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls2\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Dependency Injection:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Use a service locator like \hich\af31506\dbch\af31505\loch\f31506 get_it\hich\af31506\dbch\af31505\loch\f31506 
 or rely on Riverpod's built-in DI capabilities to manage dependencies.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 7.\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Error Handling & Logging:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Implement robust error handling (e.g., try-catch blocks, dedicated error reporting services) and logging to help debug issues.
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 
\u-10179\'3f\u-8975\'3f}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Flutter-Specific Best Practices & Technologies
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 1.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls3\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 UI Development:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Responsive Design:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Use \hich\af31506\dbch\af31505\loch\f31506 MediaQuery\hich\af31506\dbch\af31505\loch\f31506 , \hich\af31506\dbch\af31505\loch\f31506 
LayoutBuilder\hich\af31506\dbch\af31505\loch\f31506 , \hich\af31506\dbch\af31505\loch\f31506 FittedBox\hich\af31506\dbch\af31505\loch\f31506 , and responsive layout widgets to ensure your app looks good on various screen sizes. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Theming:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Define a clear theme for light and dark modes, including colors, typography, and component styles. Make it easily switchable as per user settings. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 RTL/LTR Support:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Flutter has excellent built-in support. Ensure all UI elements correctly adapt based on the selected language. Test thoroughly.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Custom Widgets:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Create reusable custom widgets for common UI patterns (e.g., appointment cards, input fields).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Smooth Scrolling & Performance:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}}\pard \rtlpar\qr \fi-360\li0\ri2160\sa160\sl278\slmult1\widctlpar
\jclisttab\tx2160\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl2\adjustright\rin0\lin2160\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Use 
\hich\af31506\dbch\af31505\loch\f31506 ListView.builder\hich\af31506\dbch\af31505\loch\f31506  \hich\af31506\dbch\af31505\loch\f31506 for long lists.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}\hich\af31506\dbch\af31505\loch\f31506 Implement \hich\af31506\dbch\af31505\loch\f31506 
AutomaticKeepAliveClientMixin\hich\af31506\dbch\af31505\loch\f31506  or \hich\af31506\dbch\af31505\loch\f31506 PageStorageKey\hich\af31506\dbch\af31505\loch\f31506  to preserve scroll positions and tab states when navigating back and forth, as requested. 

\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 2.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls3\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Navigation:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 go_router}{\rtlch\fcs1 
\ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 :}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 For a complex app with deep linking (e.g., \hich\af31506\dbch\af31505\loch\f31506 sijilli.com/username\hich\af31506\dbch\af31505\loch\f31506  ), \hich\af31506\dbch\af31505\loch\f31506 go_router\hich\af31506\dbch\af31505\loch\f31506 
 is a declarative routing package that simplifies navigation and handles deep links effectively.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 3.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls3\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Date and Time:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Custom Hijri Conversion:
}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Since you've specified }{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 not to use external packages for Hijri conversion}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
, you'll need to implement this logic yourself. This will be a significant task requiring careful research and testing to ensure accuracy for the date picker and display. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 intl}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 
\b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  package:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Useful for formatting dates, times, and numbers according to locale, but not for the core Hijri conversion if external packages are disallowed for that specific task.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 4.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls3\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Local Storage:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls3\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 shared_preferences}{
\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 :}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 For simple key-value storage like user settings (theme, language ).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 sqflite}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 
\b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  or }{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 isar}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 
\b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  / }{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 sembast}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 
\b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 :}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  \hich\af31506\dbch\af31505\loch\f31506 
If you need more complex local storage, like caching friend's names for quick invite suggestions. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 5.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls3\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Optimistic Updates:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 For a smoother UX, consider implementing optimistic updates for actions like accepting/rejecting invites or creating appointments. The UI updates immediately, and then reconciles with the backend.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 6.\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Code Generation:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Utilize packages like \hich\af31506\dbch\af31505\loch\f31506 freezed
\hich\af31506\dbch\af31505\loch\f31506  for immutable data classes and unions, and \hich\af31506\dbch\af31505\loch\f31506 json_serializable\hich\af31506\dbch\af31505\loch\f31506  for (de)serializing JSON, which will be frequent with Supabase.
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 
\u9729\'3f\loch\af45\dbch\af31505\hich\f45 \u-497\'3f}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Supabase-Specific Best Practices & Technologies
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 1.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Official Client:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Use the \hich\af31506\dbch\af31505\loch\f31506 supabase_flutter\hich\af31506\dbch\af31505\loch\f31506 
 package. It provides convenient wrappers for auth, database, realtime, and storage.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 2.\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Authentication:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Leverage Supabase Auth for email/password and potentially social providers (OAuth).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 Securely manage user sessions.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 3.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Database (PostgreSQL):}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Schema Design:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Your summarized PRD already outlines a good starting point for tables like \hich\af31506\dbch\af31505\loch\f31506 users
\hich\af31506\dbch\af31505\loch\f31506 , \hich\af31506\dbch\af31505\loch\f31506 profiles\hich\af31506\dbch\af31505\loch\f31506 , \hich\af31506\dbch\af31505\loch\f31506 appointments\hich\af31506\dbch\af31505\loch\f31506 , 
\hich\af31506\dbch\af31505\loch\f31506 invitations\hich\af31506\dbch\af31505\loch\f31506 , \hich\af31506\dbch\af31505\loch\f31506 articles\hich\af31506\dbch\af31505\loch\f31506 , etc. Ensure relationships are well-defined with foreign keys.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Row Level Security (RLS):}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  This is }{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 
\b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 critical}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
. Define RLS policies for all your tables to ensure users can only access and modify data they are permitted to. For example, a user should only see their own private appointments or appointments they are invited to. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Database Functions & Triggers:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 For complex queries, data validation, or actions that should happen atomically in the database (e.g., updating a \hich\af31506\dbch\af31505\loch\f31506 updated_at\hich\af31506\dbch\af31505\loch\f31506 
 timestamp, potentially aspects of conflict detection ).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Views:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Create database views for denormalized data or common complex queries to simplify client-side logic (e.g., a view that combines appointment data with owner and guest information).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 4.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Realtime Subscriptions:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Use Supabase Realtime for instant updates like notifications for new invites, acceptances, rejections, or changes in appointment status. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
Be mindful of the number of active subscriptions to manage costs and performance.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 5.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Storage:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Use Supabase Storage for user profile pictures, organization banners, and any other file uploads.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 Implement RLS policies for storage buckets as well.

\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 6.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Edge Functions (Serverless Functions):}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
For backend logic that shouldn't live on the client or requires elevated privileges. Examples for Sijilli: 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}}\pard \rtlpar\qr \fi-360\li0\ri2160\sa160\sl278\slmult1\widctlpar
\jclisttab\tx2160\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl2\adjustright\rin0\lin2160\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
AI-powered field filling for Organizations:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 An Edge Function could call a third-party AI/ML service or run custom logic to suggest field completions. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Sending complex notifications:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  If notifications involve multiple data lookups or integrations.

\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Admin tasks:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Approving organization upgrades. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f10\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af10\dbch\af31505\hich\f10 \'a7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Data processing:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Potentially for the "understanding the relationship between fields" for AI suggestions. 
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\f31506\kerning2\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 7.\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls4\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 API Usage:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls4\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Fetch only the data you need using \hich\af31506\dbch\af31505\loch\f31506 .select().
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 Use filters efficiently.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
Handle pagination for long lists (e.g., appointments, notifications, search results ).
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 
\u10024\'3f}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Addressing Specific "Sijilli" Features
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls5\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Dynamic Button Logic:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 This will be handled in Flutter by your state management solution, changing the button's appearance and action based on user context (owner, guest, visitor) and appointment state.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Conflict Detection:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls5\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Client-side:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 When creating/editing an appointment, fetch the user's existing appointments (where they are owner or guest) for the selected date range and check for overlaps.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Backend (Optional Enhancement):}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 A database function could also check for conflicts upon insertion/update for an additional layer of validation.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls5\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Search:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls5\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Supabase full-text search capabilities can be used for searching public appointments and user profiles.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
For invite suggestions, a combination of local caching (for speed with "friends") and backend search can be used.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls5\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Temporary Deletion (30 days):}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls5\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Add a 
\hich\af31506\dbch\af31505\loch\f31506 deleted_at\hich\af31506\dbch\af31505\loch\f31506  timestamp column to relevant tables (e.g., \hich\af31506\dbch\af31505\loch\f31506 appointments\hich\af31506\dbch\af31505\loch\f31506 , 
\hich\af31506\dbch\af31505\loch\f31506 articles\hich\af31506\dbch\af31505\loch\f31506 ).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 Use RLS policies to hide items where 
\hich\af31506\dbch\af31505\loch\f31506 deleted_at\hich\af31506\dbch\af31505\loch\f31506  is not null (unless accessed via a "trash" interface).
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
A scheduled Supabase Edge Function (or an external cron job) can periodically hard-delete items older than 30 days.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls5\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Organization Features:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls5\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Fixed Fields:}{
\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Store these preferred fields in the \hich\af31506\dbch\af31505\loch\f31506 profiles\hich\af31506\dbch\af31505\loch\f31506  or a dedicated 
\hich\af31506\dbch\af31505\loch\f31506 organization_settings\hich\af31506\dbch\af31505\loch\f31506  table.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Banner:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  A URL to an image in Supabase Storage.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls5\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Article System:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Treat articles similarly to appointments regarding creation, privacy, and invitations, but with a focus on text content.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Smart Suggestions (for forms):}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}}\pard \rtlpar\qr \fi-360\li0\ri1440\sa160\sl278\slmult1\widctlpar
\jclisttab\tx1440\wrapdefault\aspalpha\aspnum\faauto\ls5\ilvl1\adjustright\rin0\lin1440\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
Maintain a local history (e.g., using \hich\af31506\dbch\af31505\loch\f31506 sembast\hich\af31506\dbch\af31505\loch\f31506  or \hich\af31506\dbch\af31505\loch\f31506 isar\hich\af31506\dbch\af31505\loch\f31506 
) of previously used values for fields like "Topic," "Region," and "Building."
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \f2\fs20\kerning2\insrsid5584471\charrsid5584471 \hich\af2\dbch\af31505\loch\f2 o\tab}\hich\af31506\dbch\af31505\loch\f31506 
For linked fields (Region/Building), store them as pairs to provide relevant suggestions.
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 
\u-10179\'3f\u-8480\'3f}{\rtlch\fcs1 \ab\af45\alang15361 \ltrch\fcs0 \b\f45\insrsid5584471\charrsid5584471 \loch\af45\dbch\af31505\hich\f45 \u-497\'3f}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506  Development Workflow & Tools
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}\pard \rtlpar\qr \fi-360\li0\ri720\sa160\sl278\slmult1\widctlpar
\jclisttab\tx720\wrapdefault\aspalpha\aspnum\faauto\ls6\adjustright\rin0\lin720\itap0\pararsid5584471 {\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 Flutter DevTools:}{\rtlch\fcs1 
\af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  Essential for debugging UI, performance, and more.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 Supabase Studio:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506  The web interface for managing your database, auth, storage, and functions.
\par {\listtext\pard\plain\rtlpar \rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \f3\fs20\kerning2\insrsid5584471\charrsid5584471 \loch\af3\dbch\af31505\hich\f3 \'b7\tab}}{\rtlch\fcs1 \ab\af31507\alang15361 \ltrch\fcs0 \b\insrsid5584471\charrsid5584471 
\hich\af31506\dbch\af31505\loch\f31506 CI/CD:}{\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
 Set up Continuous Integration/Continuous Deployment (e.g., GitHub Actions, Codemagic) early on to automate testing and builds.
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0\pararsid5584471 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid5584471\charrsid5584471 \hich\af31506\dbch\af31505\loch\f31506 
By carefully considering these points and aligning them with your detailed PRD, you'll be well on your way to building a high-quality, user-friendly Sijilli application. Good luck!
\par }\pard \rtlpar\qr \li0\ri0\sa160\sl278\slmult1\widctlpar\wrapdefault\aspalpha\aspnum\faauto\adjustright\rin0\lin0\itap0 {\rtlch\fcs1 \af31507\alang15361 \ltrch\fcs0 \insrsid9445006 
\par }{\*\themedata 504b030414000600080000002100e9de0fbfff0000001c020000130000005b436f6e74656e745f54797065735d2e786d6cac91cb4ec3301045f748fc83e52d4a
9cb2400825e982c78ec7a27cc0c8992416c9d8b2a755fbf74cd25442a820166c2cd933f79e3be372bd1f07b5c3989ca74aaff2422b24eb1b475da5df374fd9ad
5689811a183c61a50f98f4babebc2837878049899a52a57be670674cb23d8e90721f90a4d2fa3802cb35762680fd800ecd7551dc18eb899138e3c943d7e503b6
b01d583deee5f99824e290b4ba3f364eac4a430883b3c092d4eca8f946c916422ecab927f52ea42b89a1cd59c254f919b0e85e6535d135a8de20f20b8c12c3b0
0c895fcf6720192de6bf3b9e89ecdbd6596cbcdd8eb28e7c365ecc4ec1ff1460f53fe813d3cc7f5b7f020000ffff0300504b030414000600080000002100a5d6
a7e7c0000000360100000b0000005f72656c732f2e72656c73848fcf6ac3300c87ef85bd83d17d51d2c31825762fa590432fa37d00e1287f68221bdb1bebdb4f
c7060abb0884a4eff7a93dfeae8bf9e194e720169aaa06c3e2433fcb68e1763dbf7f82c985a4a725085b787086a37bdbb55fbc50d1a33ccd311ba548b6309512
0f88d94fbc52ae4264d1c910d24a45db3462247fa791715fd71f989e19e0364cd3f51652d73760ae8fa8c9ffb3c330cc9e4fc17faf2ce545046e37944c69e462
a1a82fe353bd90a865aad41ed0b5b8f9d6fd010000ffff0300504b0304140006000800000021006b799616830000008a0000001c0000007468656d652f746865
6d652f7468656d654d616e616765722e786d6c0ccc4d0ac3201040e17da17790d93763bb284562b2cbaebbf600439c1a41c7a0d29fdbd7e5e38337cedf14d59b
4b0d592c9c070d8a65cd2e88b7f07c2ca71ba8da481cc52c6ce1c715e6e97818c9b48d13df49c873517d23d59085adb5dd20d6b52bd521ef2cdd5eb9246a3d8b
4757e8d3f729e245eb2b260a0238fd010000ffff0300504b0304140006000800000021007605574b0a0800000e220000160000007468656d652f7468656d652f
7468656d65312e786d6cec5acd8b1bc915bf07f23f347d97a7bbf53d585ef4e9597bc61e2cd9618f25a9a42e4f7597e82acd8c080bc10b81bd2c0436cb1eb290
db1e9690855dc8924bfe8decd960936cfe88bcaa6ab5aaa492e703134c98994b77e9f75efdeabd57efbdaeeefb1f5d26d43bc719272c6df9e1bdc0f7703a6153
92ce5bfef3d1a0d4f03d2e503a4594a5b8e5af30f73f7af0eb5fdd478722c609f6403ee587a8e5c7422c0e0f0ef8048611bfc7163885df662c4b9080db6c7e30
cdd005e84de8411404b5830491d4f7529480da9f7fffcf1f7e7ee53d9dcdc804fb0fd6dafb14a648059703139a0da56e9c8b18d8e95928117cc5bb34f3ce116d
f930d1945d8cf0a5f03d8ab8801f5a7ea0fefc8307f70fd0612e44c51e59436ea0fe72b95c607a16a939b3f9b89834e8478d4a58e857002a7671fd86fc2ff429
009a4c60a59a8ba933acd6824694630d90be74e86ed6c3b28d37f497773887cd5a27aa58fa1548ebafece08341b3dfab5a7805d2f8ea0ebe1d449d66d9c22b90
c6d776f0957ebb1ef52dbc02c594a467bbe85abdd1a8e5e8023263f4c8096fd66a41bd97c33728888622bae41433968a7db196a0972c1b004002291224f5c46a
8167680261dc5e08c6bd1ee10b8a56beb74029e3301c446108a15709a2e25f591c1d6264484b5ec084ef0c493e1e9f6464215afe23d0ea1b90373ffdf4fad58f
af5ffdedf5679fbd7ef557ef98cc63a155597247289d9b72bf7cfb87ff7cf33befdf3ffcf9972fffe8c67313fff62f9fbffdfb3fdea51eb6dac6146fbefafeed
8fdfbff9fa8b7f7df7a5437b3b4363133e2209e6de137ce13d63092c5099c2e68fc7d9cd24463122a6443b9d739422398b437f5fc416fac90a51e4c075b06dc7
1719a41a17f0e1f2a5457818674b411c1a1fc789053c618c7658e6b4c263399761e6d1329dbb27cf9626ee1942e7aeb9bb28b5bcdc5f2e20c71297ca6e8c2d9a
a714a502cd718a85277f6367183b56f70921965d4fc824639ccd84f709f13a88384d3222632b9a3642472401bfac5c04c1df966d4e5e781d465dabeee1731b09
7b035107f911a696191fa2a540894be50825d434f83112b18be470954d4c5c9f0bf0f41c53e6f5a7987397ccd30cd66b38fd3182ece674fb095d25363213e4cc
a5f3183166227becac1ba364e1c20e491a9bd88ff9198428f24e9970c14f98bd43e43df801a57bddfd8260cbdd576783e790e54c4a9b0091bf2c33872f1f6266
c5ef70456708bb524d3b4bac14dbce88333a3acbb915dac718537481a6187bcf3f7630e8b08565f30de94731649523ec0aac47c88e55799f628e3dd5dcece6c9
63c2ad901de239dbc3e764b5957856284d50b64ff313f0ba69f3fe3883cde858e7533a3933814f083481102f4ea33ce5a0c308eebd5a4f6364153079cfddf1ba
ca2cff5d678fc1be7c69d1b8c6be04197c631948eca6cc3b6d3342d49a6013302344bc6357ba0511cbfd1b11595c95d8d22937b337edc60dd01d594d4f42d22b
3aa0ff5de703fdc59b3f7de308c1f7d3edb8155ba9ea867dcebe5472b4d5ddecc36df7345d964dc987dfd2f4d0323dc5504576f3d55d4773d7d1f8fff71dcdbe
fd7cd7c7eceb36eefa181ffa8bbb3e263f5a793f7dcca67581ae461e2fe8631e75e893ec3df399114a876245f13157c73e1c9e66a603189472eac0131767808b
182e659983092cdc3c434ac6cb98f80d11f130460b381b0a7da964ce73d573ee2d1887232335ecd42df174999cb0a93eea54674b81aeac1c89cd78508543273d
0ec75442a36bf57c50f253e7a9c057b19dab63d63501297b1312c6643689b283447d3d780509796af67e58341d2c1a52fdda553ba6006a8557e071db8387f496
5fad48427048ce27d09a4fa59fb4abd7de55ce7c9f9ede674c2b02e05851af044ee50b4f3725d7bdcb93abd3a1760d4f5b2494537458d92494655483c7637808
cea3538e5e87c64d7ddddcb8d4a2274da1e683f8ded0a837dec5e2b6be06b9eddc40533353d0d4bb803d1ec1a6f3bd095ab4fc199c19c365b280e0e1f2910bd1
39bc7999884ceff8dba49645c6450ff1585b5c651ded9f84089c7994242d5faebff0034d5512d1e49ab0753f547291dc701f1a39f0baed653c9be18930fd6e8c
484beb5b48f13a59387f55e2b7074b49b604770fe3e98537a6cbec198210abd643e9dd29e1f0ea20d4ae9e1278175664b24dfc6d55a63cfb9b2fa3540ce97144
1731ca4b8a99cd355c1594828eba2b6c60dce56b06831a26c92be1782e2bac6954ab9c16b54b73d85b76af16929633b2e6a6685a6945964d771ab36658d7812d
5bdeaeca1bacd62686a46696789dbbb7736e739decb61a85a24c80c10bfbddaef61bd4369359d424e3dd3c2c93763e6a178ff502afa0769d2a61a4fdda5aed96
dd8a22e19c0e066f55fa416e3b6a6168b66e2c95a5d55b73f3bd361bbf84e4d183367749f59b6e9ac29d8c4abe38cd946fc76cbaca2f29d78946fb5c36a51249
d36778e691e965cb8f5c9da37edb1ae6dd80424b3159bc0a4167b7670be67829aa376c21ac03bc082abd296de14242cd0cbd7721ac4e145db4c5e59ab2ecd501
af4cc8f5aac1b4b9a5e06ad78af0ea3f43d0db0e5567a7732fd0be14797e812b6f999196ffdba0daae74a36ab71434aafd52a55c094a8d6abb5c6a57abe5b05f
0d835e27fa14e8893809abfab38701bc04a2abfce30735bef30144b27ecf756fc29203a6be6c3850de571f408491f50184fe9ac11bc90f1c7c7024d08afa6125
6a47dd52b717d64a95a8572b35eae576a91bd57a511b8a766dd0fed4f7ce1538ecf47a8341352ad5ba80ab04ed6aa9dd29774bb546bf130dc27ea51700382f3f
97f01403365bdb022e15af07ff050000ffff0300504b0304140006000800000021000dd1909fb60000001b010000270000007468656d652f7468656d652f5f72
656c732f7468656d654d616e616765722e786d6c2e72656c73848f4d0ac2301484f78277086f6fd3ba109126dd88d0add40384e4350d363f2451eced0dae2c08
2e8761be9969bb979dc9136332de3168aa1a083ae995719ac16db8ec8e4052164e89d93b64b060828e6f37ed1567914b284d262452282e3198720e274a939cd0
8a54f980ae38a38f56e422a3a641c8bbd048f7757da0f19b017cc524bd62107bd5001996509affb3fd381a89672f1f165dfe514173d9850528a2c6cce0239baa
4c04ca5bbabac4df000000ffff0300504b01022d0014000600080000002100e9de0fbfff0000001c0200001300000000000000000000000000000000005b436f
6e74656e745f54797065735d2e786d6c504b01022d0014000600080000002100a5d6a7e7c0000000360100000b00000000000000000000000000300100005f72
656c732f2e72656c73504b01022d00140006000800000021006b799616830000008a0000001c00000000000000000000000000190200007468656d652f746865
6d652f7468656d654d616e616765722e786d6c504b01022d00140006000800000021007605574b0a0800000e2200001600000000000000000000000000d60200
007468656d652f7468656d652f7468656d65312e786d6c504b01022d00140006000800000021000dd1909fb60000001b01000027000000000000000000000000
00140b00007468656d652f7468656d652f5f72656c732f7468656d654d616e616765722e786d6c2e72656c73504b050600000000050005005d0100000f0c00000000}
{\*\colorschememapping 3c3f786d6c2076657273696f6e3d22312e302220656e636f64696e673d225554462d3822207374616e64616c6f6e653d22796573223f3e0d0a3c613a636c724d
617020786d6c6e733a613d22687474703a2f2f736368656d61732e6f70656e786d6c666f726d6174732e6f72672f64726177696e676d6c2f323030362f6d6169
6e22206267313d226c743122207478313d22646b3122206267323d226c743222207478323d22646b322220616363656e74313d22616363656e74312220616363
656e74323d22616363656e74322220616363656e74333d22616363656e74332220616363656e74343d22616363656e74342220616363656e74353d22616363656e74352220616363656e74363d22616363656e74362220686c696e6b3d22686c696e6b2220666f6c486c696e6b3d22666f6c486c696e6b222f3e}
{\*\latentstyles\lsdstimax376\lsdlockeddef0\lsdsemihiddendef0\lsdunhideuseddef0\lsdqformatdef0\lsdprioritydef99{\lsdlockedexcept \lsdqformat1 \lsdpriority0 \lsdlocked0 Normal;\lsdqformat1 \lsdpriority9 \lsdlocked0 heading 1;
\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 2;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 3;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 4;
\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 5;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 6;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 7;
\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 8;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority9 \lsdlocked0 heading 9;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 1;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 5;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 6;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 7;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 8;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index 9;
\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 1;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 2;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 3;
\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 4;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 5;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 6;
\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 7;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 8;\lsdsemihidden1 \lsdunhideused1 \lsdpriority39 \lsdlocked0 toc 9;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Normal Indent;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 footnote text;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 annotation text;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 header;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 footer;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 index heading;\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority35 \lsdlocked0 caption;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 table of figures;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 envelope address;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 envelope return;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 footnote reference;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 annotation reference;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 line number;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 page number;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 endnote reference;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 endnote text;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 table of authorities;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 macro;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 toa heading;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Bullet;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Number;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List 3;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Bullet 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Bullet 3;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Bullet 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Bullet 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Number 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Number 3;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Number 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Number 5;\lsdqformat1 \lsdpriority10 \lsdlocked0 Title;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Closing;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Signature;\lsdsemihidden1 \lsdunhideused1 \lsdpriority1 \lsdlocked0 Default Paragraph Font;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text Indent;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Continue;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Continue 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Continue 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Continue 4;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 List Continue 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Message Header;\lsdqformat1 \lsdpriority11 \lsdlocked0 Subtitle;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Salutation;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Date;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text First Indent;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text First Indent 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Note Heading;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text Indent 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Body Text Indent 3;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Block Text;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Hyperlink;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 FollowedHyperlink;\lsdqformat1 \lsdpriority22 \lsdlocked0 Strong;
\lsdqformat1 \lsdpriority20 \lsdlocked0 Emphasis;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Document Map;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Plain Text;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 E-mail Signature;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Top of Form;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Bottom of Form;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Normal (Web);\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Acronym;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Address;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Cite;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Code;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Definition;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Keyboard;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Preformatted;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Sample;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Typewriter;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 HTML Variable;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Normal Table;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 annotation subject;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 No List;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Outline List 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Outline List 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Outline List 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Simple 1;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Simple 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Simple 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Classic 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Classic 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Classic 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Classic 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Colorful 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Colorful 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Colorful 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Columns 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Columns 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Columns 3;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Columns 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Columns 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 6;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 7;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Grid 8;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 4;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 5;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 6;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 7;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table List 8;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table 3D effects 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table 3D effects 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table 3D effects 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Contemporary;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Elegant;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Professional;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Subtle 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Subtle 2;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Web 1;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Web 2;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Web 3;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Balloon Text;\lsdpriority39 \lsdlocked0 Table Grid;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Table Theme;\lsdsemihidden1 \lsdlocked0 Placeholder Text;
\lsdqformat1 \lsdpriority1 \lsdlocked0 No Spacing;\lsdpriority60 \lsdlocked0 Light Shading;\lsdpriority61 \lsdlocked0 Light List;\lsdpriority62 \lsdlocked0 Light Grid;\lsdpriority63 \lsdlocked0 Medium Shading 1;\lsdpriority64 \lsdlocked0 Medium Shading 2;
\lsdpriority65 \lsdlocked0 Medium List 1;\lsdpriority66 \lsdlocked0 Medium List 2;\lsdpriority67 \lsdlocked0 Medium Grid 1;\lsdpriority68 \lsdlocked0 Medium Grid 2;\lsdpriority69 \lsdlocked0 Medium Grid 3;\lsdpriority70 \lsdlocked0 Dark List;
\lsdpriority71 \lsdlocked0 Colorful Shading;\lsdpriority72 \lsdlocked0 Colorful List;\lsdpriority73 \lsdlocked0 Colorful Grid;\lsdpriority60 \lsdlocked0 Light Shading Accent 1;\lsdpriority61 \lsdlocked0 Light List Accent 1;
\lsdpriority62 \lsdlocked0 Light Grid Accent 1;\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 1;\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 1;\lsdpriority65 \lsdlocked0 Medium List 1 Accent 1;\lsdsemihidden1 \lsdlocked0 Revision;
\lsdqformat1 \lsdpriority34 \lsdlocked0 List Paragraph;\lsdqformat1 \lsdpriority29 \lsdlocked0 Quote;\lsdqformat1 \lsdpriority30 \lsdlocked0 Intense Quote;\lsdpriority66 \lsdlocked0 Medium List 2 Accent 1;\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 1;
\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 1;\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 1;\lsdpriority70 \lsdlocked0 Dark List Accent 1;\lsdpriority71 \lsdlocked0 Colorful Shading Accent 1;\lsdpriority72 \lsdlocked0 Colorful List Accent 1;
\lsdpriority73 \lsdlocked0 Colorful Grid Accent 1;\lsdpriority60 \lsdlocked0 Light Shading Accent 2;\lsdpriority61 \lsdlocked0 Light List Accent 2;\lsdpriority62 \lsdlocked0 Light Grid Accent 2;\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 2;
\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 2;\lsdpriority65 \lsdlocked0 Medium List 1 Accent 2;\lsdpriority66 \lsdlocked0 Medium List 2 Accent 2;\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 2;\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 2;
\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 2;\lsdpriority70 \lsdlocked0 Dark List Accent 2;\lsdpriority71 \lsdlocked0 Colorful Shading Accent 2;\lsdpriority72 \lsdlocked0 Colorful List Accent 2;\lsdpriority73 \lsdlocked0 Colorful Grid Accent 2;
\lsdpriority60 \lsdlocked0 Light Shading Accent 3;\lsdpriority61 \lsdlocked0 Light List Accent 3;\lsdpriority62 \lsdlocked0 Light Grid Accent 3;\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 3;\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 3;
\lsdpriority65 \lsdlocked0 Medium List 1 Accent 3;\lsdpriority66 \lsdlocked0 Medium List 2 Accent 3;\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 3;\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 3;\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 3;
\lsdpriority70 \lsdlocked0 Dark List Accent 3;\lsdpriority71 \lsdlocked0 Colorful Shading Accent 3;\lsdpriority72 \lsdlocked0 Colorful List Accent 3;\lsdpriority73 \lsdlocked0 Colorful Grid Accent 3;\lsdpriority60 \lsdlocked0 Light Shading Accent 4;
\lsdpriority61 \lsdlocked0 Light List Accent 4;\lsdpriority62 \lsdlocked0 Light Grid Accent 4;\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 4;\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 4;\lsdpriority65 \lsdlocked0 Medium List 1 Accent 4;
\lsdpriority66 \lsdlocked0 Medium List 2 Accent 4;\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 4;\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 4;\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 4;\lsdpriority70 \lsdlocked0 Dark List Accent 4;
\lsdpriority71 \lsdlocked0 Colorful Shading Accent 4;\lsdpriority72 \lsdlocked0 Colorful List Accent 4;\lsdpriority73 \lsdlocked0 Colorful Grid Accent 4;\lsdpriority60 \lsdlocked0 Light Shading Accent 5;\lsdpriority61 \lsdlocked0 Light List Accent 5;
\lsdpriority62 \lsdlocked0 Light Grid Accent 5;\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 5;\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 5;\lsdpriority65 \lsdlocked0 Medium List 1 Accent 5;\lsdpriority66 \lsdlocked0 Medium List 2 Accent 5;
\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 5;\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 5;\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 5;\lsdpriority70 \lsdlocked0 Dark List Accent 5;\lsdpriority71 \lsdlocked0 Colorful Shading Accent 5;
\lsdpriority72 \lsdlocked0 Colorful List Accent 5;\lsdpriority73 \lsdlocked0 Colorful Grid Accent 5;\lsdpriority60 \lsdlocked0 Light Shading Accent 6;\lsdpriority61 \lsdlocked0 Light List Accent 6;\lsdpriority62 \lsdlocked0 Light Grid Accent 6;
\lsdpriority63 \lsdlocked0 Medium Shading 1 Accent 6;\lsdpriority64 \lsdlocked0 Medium Shading 2 Accent 6;\lsdpriority65 \lsdlocked0 Medium List 1 Accent 6;\lsdpriority66 \lsdlocked0 Medium List 2 Accent 6;
\lsdpriority67 \lsdlocked0 Medium Grid 1 Accent 6;\lsdpriority68 \lsdlocked0 Medium Grid 2 Accent 6;\lsdpriority69 \lsdlocked0 Medium Grid 3 Accent 6;\lsdpriority70 \lsdlocked0 Dark List Accent 6;\lsdpriority71 \lsdlocked0 Colorful Shading Accent 6;
\lsdpriority72 \lsdlocked0 Colorful List Accent 6;\lsdpriority73 \lsdlocked0 Colorful Grid Accent 6;\lsdqformat1 \lsdpriority19 \lsdlocked0 Subtle Emphasis;\lsdqformat1 \lsdpriority21 \lsdlocked0 Intense Emphasis;
\lsdqformat1 \lsdpriority31 \lsdlocked0 Subtle Reference;\lsdqformat1 \lsdpriority32 \lsdlocked0 Intense Reference;\lsdqformat1 \lsdpriority33 \lsdlocked0 Book Title;\lsdsemihidden1 \lsdunhideused1 \lsdpriority37 \lsdlocked0 Bibliography;
\lsdsemihidden1 \lsdunhideused1 \lsdqformat1 \lsdpriority39 \lsdlocked0 TOC Heading;\lsdpriority41 \lsdlocked0 Plain Table 1;\lsdpriority42 \lsdlocked0 Plain Table 2;\lsdpriority43 \lsdlocked0 Plain Table 3;\lsdpriority44 \lsdlocked0 Plain Table 4;
\lsdpriority45 \lsdlocked0 Plain Table 5;\lsdpriority40 \lsdlocked0 Grid Table Light;\lsdpriority46 \lsdlocked0 Grid Table 1 Light;\lsdpriority47 \lsdlocked0 Grid Table 2;\lsdpriority48 \lsdlocked0 Grid Table 3;\lsdpriority49 \lsdlocked0 Grid Table 4;
\lsdpriority50 \lsdlocked0 Grid Table 5 Dark;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful;\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful;\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 1;\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 1;
\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 1;\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 1;\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 1;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 1;
\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 1;\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 2;\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 2;\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 2;
\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 2;\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 2;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 2;\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 2;
\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 3;\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 3;\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 3;\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 3;
\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 3;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 3;\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 3;\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 4;
\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 4;\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 4;\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 4;\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 4;
\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 4;\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 4;\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 5;\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 5;
\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 5;\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 5;\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 5;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 5;
\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 5;\lsdpriority46 \lsdlocked0 Grid Table 1 Light Accent 6;\lsdpriority47 \lsdlocked0 Grid Table 2 Accent 6;\lsdpriority48 \lsdlocked0 Grid Table 3 Accent 6;
\lsdpriority49 \lsdlocked0 Grid Table 4 Accent 6;\lsdpriority50 \lsdlocked0 Grid Table 5 Dark Accent 6;\lsdpriority51 \lsdlocked0 Grid Table 6 Colorful Accent 6;\lsdpriority52 \lsdlocked0 Grid Table 7 Colorful Accent 6;
\lsdpriority46 \lsdlocked0 List Table 1 Light;\lsdpriority47 \lsdlocked0 List Table 2;\lsdpriority48 \lsdlocked0 List Table 3;\lsdpriority49 \lsdlocked0 List Table 4;\lsdpriority50 \lsdlocked0 List Table 5 Dark;
\lsdpriority51 \lsdlocked0 List Table 6 Colorful;\lsdpriority52 \lsdlocked0 List Table 7 Colorful;\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 1;\lsdpriority47 \lsdlocked0 List Table 2 Accent 1;\lsdpriority48 \lsdlocked0 List Table 3 Accent 1;
\lsdpriority49 \lsdlocked0 List Table 4 Accent 1;\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 1;\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 1;\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 1;
\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 2;\lsdpriority47 \lsdlocked0 List Table 2 Accent 2;\lsdpriority48 \lsdlocked0 List Table 3 Accent 2;\lsdpriority49 \lsdlocked0 List Table 4 Accent 2;
\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 2;\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 2;\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 2;\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 3;
\lsdpriority47 \lsdlocked0 List Table 2 Accent 3;\lsdpriority48 \lsdlocked0 List Table 3 Accent 3;\lsdpriority49 \lsdlocked0 List Table 4 Accent 3;\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 3;
\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 3;\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 3;\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 4;\lsdpriority47 \lsdlocked0 List Table 2 Accent 4;
\lsdpriority48 \lsdlocked0 List Table 3 Accent 4;\lsdpriority49 \lsdlocked0 List Table 4 Accent 4;\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 4;\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 4;
\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 4;\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 5;\lsdpriority47 \lsdlocked0 List Table 2 Accent 5;\lsdpriority48 \lsdlocked0 List Table 3 Accent 5;
\lsdpriority49 \lsdlocked0 List Table 4 Accent 5;\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 5;\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 5;\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 5;
\lsdpriority46 \lsdlocked0 List Table 1 Light Accent 6;\lsdpriority47 \lsdlocked0 List Table 2 Accent 6;\lsdpriority48 \lsdlocked0 List Table 3 Accent 6;\lsdpriority49 \lsdlocked0 List Table 4 Accent 6;
\lsdpriority50 \lsdlocked0 List Table 5 Dark Accent 6;\lsdpriority51 \lsdlocked0 List Table 6 Colorful Accent 6;\lsdpriority52 \lsdlocked0 List Table 7 Colorful Accent 6;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Mention;
\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Smart Hyperlink;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Hashtag;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Unresolved Mention;\lsdsemihidden1 \lsdunhideused1 \lsdlocked0 Smart Link;}}{\*\datastore 01050000
02000000180000004d73786d6c322e534158584d4c5265616465722e362e3000000000000000000000060000
d0cf11e0a1b11ae1000000000000000000000000000000003e000300feff090006000000000000000000000001000000010000000000000000100000feffffff00000000feffffff0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
fffffffffffffffffdfffffffeffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
ffffffffffffffffffffffffffffffff52006f006f007400200045006e00740072007900000000000000000000000000000000000000000000000000000000000000000000000000000000000000000016000500ffffffffffffffffffffffff0c6ad98892f1d411a65f0040963251e5000000000000000000000000f08a
da9e6bd1db01feffffff00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffff00000000000000000000000000000000000000000000000000000000
00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffff000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000105000000000000}}