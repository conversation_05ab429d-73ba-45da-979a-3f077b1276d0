import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/user_entity.dart';

/// Repository interface for user-related operations
abstract class UserRepository {
  /// Get current authenticated user
  Future<Either<Failure, UserEntity>> getCurrentUser();

  /// Get user by ID
  Future<Either<Failure, UserEntity>> getUserById(String userId);

  /// Get user by username
  Future<Either<Failure, UserEntity>> getUserByUsername(String username);

  /// Update user profile
  Future<Either<Failure, UserEntity>> updateUserProfile(UserEntity user);

  /// Upload user avatar
  Future<Either<Failure, String>> uploadAvatar(String filePath);

  /// Search users
  Future<Either<Failure, List<UserEntity>>> searchUsers({
    required String query,
    int page = 1,
    int limit = 20,
  });

  /// Get user followers
  Future<Either<Failure, List<UserEntity>>> getUserFollowers({
    required String userId,
    int page = 1,
    int limit = 20,
  });

  /// Get user following
  Future<Either<Failure, List<UserEntity>>> getUserFollowing({
    required String userId,
    int page = 1,
    int limit = 20,
  });

  /// Follow user
  Future<Either<Failure, void>> followUser(String userId);

  /// Unfollow user
  Future<Either<Failure, void>> unfollowUser(String userId);

  /// Check if user is following another user
  Future<Either<Failure, bool>> isFollowing(String userId);

  /// Get user statistics
  Future<Either<Failure, UserStats>> getUserStats(String userId);

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount();
}

/// User statistics model
class UserStats {
  final int appointmentsCount;
  final int articlesCount;
  final int followersCount;
  final int followingCount;
  final int publicAppointmentsCount;
  final int publicArticlesCount;

  const UserStats({
    required this.appointmentsCount,
    required this.articlesCount,
    required this.followersCount,
    required this.followingCount,
    required this.publicAppointmentsCount,
    required this.publicArticlesCount,
  });
}
