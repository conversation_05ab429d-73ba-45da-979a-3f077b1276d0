import 'package:intl/intl.dart';

/// Utility class for date and time operations
class DateUtils {
  // Date formatters for Arabic locale
  static final DateFormat _arabicDateFormat = DateFormat('dd/MM/yyyy', 'ar');
  static final DateFormat _arabicTimeFormat = DateFormat('HH:mm', 'ar');
  static final DateFormat _arabicDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm', 'ar');
  static final DateFormat _arabicDayFormat = DateFormat('EEEE', 'ar');
  static final DateFormat _arabicMonthFormat = DateFormat('MMMM', 'ar');
  
  // Date formatters for English locale
  static final DateFormat _englishDateFormat = DateFormat('dd/MM/yyyy', 'en');
  static final DateFormat _englishTimeFormat = DateFormat('HH:mm', 'en');
  static final DateFormat _englishDateTimeFormat = DateFormat('dd/MM/yyyy HH:mm', 'en');
  static final DateFormat _englishDayFormat = DateFormat('EEEE', 'en');
  static final DateFormat _englishMonthFormat = DateFormat('MMMM', 'en');

  /// Format date for display based on locale
  static String formatDate(DateTime date, {String locale = 'ar'}) {
    if (locale == 'ar') {
      return _arabicDateFormat.format(date);
    }
    return _englishDateFormat.format(date);
  }

  /// Format time for display based on locale
  static String formatTime(DateTime time, {String locale = 'ar'}) {
    if (locale == 'ar') {
      return _arabicTimeFormat.format(time);
    }
    return _englishTimeFormat.format(time);
  }

  /// Format date and time for display based on locale
  static String formatDateTime(DateTime dateTime, {String locale = 'ar'}) {
    if (locale == 'ar') {
      return _arabicDateTimeFormat.format(dateTime);
    }
    return _englishDateTimeFormat.format(dateTime);
  }

  /// Get day name based on locale
  static String getDayName(DateTime date, {String locale = 'ar'}) {
    if (locale == 'ar') {
      return _arabicDayFormat.format(date);
    }
    return _englishDayFormat.format(date);
  }

  /// Get month name based on locale
  static String getMonthName(DateTime date, {String locale = 'ar'}) {
    if (locale == 'ar') {
      return _arabicMonthFormat.format(date);
    }
    return _englishMonthFormat.format(date);
  }

  /// Get relative time string (e.g., "منذ ساعة", "قبل يومين")
  static String getRelativeTime(DateTime dateTime, {String locale = 'ar'}) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (locale == 'ar') {
      if (difference.inMinutes < 1) {
        return 'الآن';
      } else if (difference.inMinutes < 60) {
        return 'منذ ${difference.inMinutes} دقيقة';
      } else if (difference.inHours < 24) {
        return 'منذ ${difference.inHours} ساعة';
      } else if (difference.inDays < 7) {
        return 'منذ ${difference.inDays} يوم';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return 'منذ $weeks أسبوع';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return 'منذ $months شهر';
      } else {
        final years = (difference.inDays / 365).floor();
        return 'منذ $years سنة';
      }
    } else {
      if (difference.inMinutes < 1) {
        return 'Now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes} minutes ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours} hours ago';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return '$weeks weeks ago';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return '$months months ago';
      } else {
        final years = (difference.inDays / 365).floor();
        return '$years years ago';
      }
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year &&
           date.month == tomorrow.month &&
           date.day == tomorrow.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
           date.month == yesterday.month &&
           date.day == yesterday.day;
  }

  /// Get start of day
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// Get end of day
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// Get start of week (Monday)
  static DateTime startOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return startOfDay(date.subtract(Duration(days: daysFromMonday)));
  }

  /// Get end of week (Sunday)
  static DateTime endOfWeek(DateTime date) {
    final daysToSunday = 7 - date.weekday;
    return endOfDay(date.add(Duration(days: daysToSunday)));
  }

  /// Get start of month
  static DateTime startOfMonth(DateTime date) {
    return DateTime(date.year, date.month, 1);
  }

  /// Get end of month
  static DateTime endOfMonth(DateTime date) {
    final nextMonth = date.month == 12 
        ? DateTime(date.year + 1, 1, 1)
        : DateTime(date.year, date.month + 1, 1);
    return nextMonth.subtract(const Duration(days: 1));
  }

  /// Convert Hijri date to Gregorian (placeholder implementation)
  static DateTime? hijriToGregorian(DateTime hijriDate, {int correction = 0}) {
    // TODO: Implement proper Hijri to Gregorian conversion
    // This is a placeholder implementation
    return hijriDate.add(Duration(days: correction));
  }

  /// Convert Gregorian date to Hijri (placeholder implementation)
  static DateTime? gregorianToHijri(DateTime gregorianDate, {int correction = 0}) {
    // TODO: Implement proper Gregorian to Hijri conversion
    // This is a placeholder implementation
    return gregorianDate.subtract(Duration(days: correction));
  }
}
