import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/auth_button.dart';
import '../../providers/auth/auth_bloc.dart';
import '../../providers/auth/auth_event.dart';
import '../../providers/auth/auth_state.dart';
import 'login_screen.dart';

/// Registration screen for new user sign up
class RegisterScreen extends StatefulWidget {
  static const String routeName = '/register';

  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _fullNameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.onSurface),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            setState(() {
              _isLoading = true;
            });
          } else {
            setState(() {
              _isLoading = false;
            });
          }

          if (state is AuthAuthenticated) {
            // Navigate to home screen
            Navigator.of(context).pushReplacementNamed('/home');
          } else if (state is AuthSignUpSuccess) {
            // Show success message and navigate to login
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.primary,
              ),
            );
            Navigator.of(context).pushReplacementNamed(LoginScreen.routeName);
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 20),

                  // Title and Subtitle
                  _buildHeader(l10n),

                  const SizedBox(height: 32),

                  // Full Name Field
                  AuthTextField(
                    label: l10n.fullName,
                    hint: l10n.fullNameHint,
                    controller: _fullNameController,
                    prefixIcon: const Icon(Icons.person_outline),
                    validator: (value) => _validateFullName(value, l10n),
                    enabled: !_isLoading,
                    autofocus: true,
                    onSubmitted: (_) => _focusNext(),
                  ),

                  const SizedBox(height: 16),

                  // Username Field
                  AuthTextField(
                    label: l10n.username,
                    hint: l10n.usernameHint,
                    controller: _usernameController,
                    prefixIcon: const Icon(Icons.alternate_email),
                    validator: (value) => _validateUsername(value, l10n),
                    enabled: !_isLoading,
                    onSubmitted: (_) => _focusNext(),
                  ),

                  const SizedBox(height: 16),

                  // Email Field
                  EmailTextField(
                    controller: _emailController,
                    enabled: !_isLoading,
                    onSubmitted: (_) => _focusNext(),
                  ),

                  const SizedBox(height: 16),

                  // Password Field
                  PasswordTextField(
                    controller: _passwordController,
                    enabled: !_isLoading,
                    onSubmitted: (_) => _focusNext(),
                  ),

                  const SizedBox(height: 16),

                  // Confirm Password Field
                  PasswordTextField(
                    controller: _confirmPasswordController,
                    label: l10n.confirmPassword,
                    hint: l10n.confirmPasswordHint,
                    validator: (value) => _validateConfirmPassword(value, l10n),
                    enabled: !_isLoading,
                    textInputAction: TextInputAction.done,
                    onSubmitted: (_) => _signUp(),
                  ),

                  const SizedBox(height: 32),

                  // Sign Up Button
                  AuthButton(
                    text: l10n.signUp,
                    onPressed: _isLoading ? null : _signUp,
                    isLoading: _isLoading,
                  ),

                  const SizedBox(height: 24),

                  // Divider
                  _buildDivider(l10n),

                  const SizedBox(height: 24),

                  // Social Login Buttons
                  _buildSocialLoginButtons(l10n),

                  const SizedBox(height: 32),

                  // Sign In Link
                  AuthLinkButton(
                    text: l10n.alreadyHaveAccount,
                    linkText: l10n.signIn,
                    onPressed: _navigateToSignIn,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(AppLocalizations l10n) {
    return Column(
      children: [
        Text(
          l10n.signUpTitle,
          style: AppTextStyles.headlineLarge.copyWith(
            color: AppColors.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          l10n.signUpSubtitle,
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDivider(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: AppColors.outline.withOpacity(0.3),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            l10n.orContinueWith,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurface.withOpacity(0.6),
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: AppColors.outline.withOpacity(0.3),
            thickness: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildSocialLoginButtons(AppLocalizations l10n) {
    return Column(
      children: [
        // Google Sign In
        SocialLoginButton(
          text: l10n.signInWithGoogle,
          icon: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(Icons.g_mobiledata, color: Colors.red, size: 20),
          ),
          onPressed: _isLoading ? null : () => _signInWithProvider('google'),
          isLoading: false,
        ),

        const SizedBox(height: 12),

        // Apple Sign In (iOS only)
        if (Theme.of(context).platform == TargetPlatform.iOS)
          SocialLoginButton(
            text: l10n.signInWithApple,
            icon: const Icon(Icons.apple, color: Colors.white, size: 24),
            onPressed: _isLoading ? null : () => _signInWithProvider('apple'),
            isLoading: false,
            backgroundColor: AppColors.onSurface,
            textColor: AppColors.surface,
          ),
      ],
    );
  }

  String? _validateFullName(String? value, AppLocalizations l10n) {
    if (value == null || value.trim().isEmpty) {
      return l10n.fullNameRequired;
    }
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  String? _validateUsername(String? value, AppLocalizations l10n) {
    if (value == null || value.trim().isEmpty) {
      return l10n.usernameRequired;
    }

    final username = value.trim();
    if (username.length < 3 || username.length > 24) {
      return l10n.usernameInvalid;
    }

    final usernameRegex = RegExp(r'^[a-zA-Z0-9_]+$');
    if (!usernameRegex.hasMatch(username)) {
      return l10n.usernameInvalid;
    }

    return null;
  }

  String? _validateConfirmPassword(String? value, AppLocalizations l10n) {
    if (value == null || value.isEmpty) {
      return l10n.passwordRequired;
    }

    if (value != _passwordController.text) {
      return l10n.passwordsDoNotMatch;
    }

    return null;
  }

  void _focusNext() {
    FocusScope.of(context).nextFocus();
  }

  void _signUp() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
        AuthSignUpRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          fullName: _fullNameController.text.trim(),
          username: _usernameController.text.trim(),
        ),
      );
    }
  }

  void _signInWithProvider(String provider) {
    context.read<AuthBloc>().add(AuthOAuthSignInRequested(provider: provider));
  }

  void _navigateToSignIn() {
    Navigator.of(context).pop();
  }
}
