# Authentication System Documentation

## Overview

This document describes the authentication system implemented in the Sijilli app using Supabase and following Clean Architecture principles.

## Architecture

The authentication system follows Clean Architecture with three main layers:

### 1. Domain Layer
- **Entities**: `AuthEntity`, `LoginRequest`, `RegisterRequest`, `PasswordResetRequest`
- **Repository Interface**: `AuthRepository`
- **Use Cases**: 
  - `LoginUser`
  - `RegisterUser` 
  - `ResetPassword`
  - `LogoutUser`

### 2. Data Layer
- **Models**: `AuthModel`, `LoginRequestModel`, `RegisterRequestModel`
- **Data Sources**: `AuthRemoteDataSource` (Supabase implementation)
- **Repository Implementation**: `AuthRepositoryImpl`

### 3. Presentation Layer
- **BLoC**: `AuthBloc` with `AuthEvent` and `AuthState`
- **Screens**: 
  - `LoginScreen`
  - `RegisterScreen`
  - `ForgotPasswordScreen`
- **Widgets**: `AuthTextField`, `AuthButton`, `SocialLoginButton`

## Features

### Authentication Methods
1. **Email/Password Authentication**
   - Sign in with email and password
   - Sign up with email, password, full name, and username
   - Email verification (configurable)

2. **Password Reset**
   - Send password reset email
   - Handle password reset flow

3. **Social Authentication** (Ready for implementation)
   - Google Sign In
   - Apple Sign In (iOS only)
   - OAuth provider support

4. **Magic Link Authentication** (Ready for implementation)
   - Passwordless authentication via email

### Security Features
- Row Level Security (RLS) policies in Supabase
- JWT token validation
- Session management with refresh tokens
- Secure password requirements

## Screens

### 1. Login Screen (`/login`)
- Email and password fields with validation
- "Forgot Password?" link
- Social login buttons
- Navigation to registration screen
- Loading states and error handling

### 2. Registration Screen (`/register`)
- Full name, username, email, and password fields
- Password confirmation
- Form validation
- Social registration options
- Navigation to login screen

### 3. Forgot Password Screen (`/forgot-password`)
- Email input for password reset
- Success state with confirmation message
- Resend functionality
- Navigation back to login

## Configuration

### Supabase Setup
The app is configured to use Supabase with the following settings:

```dart
// lib/core/config/app_config.dart
static const String supabaseUrl = 'https://czofsbnnrwhqfolgqmkz.supabase.co';
static const String supabaseAnonKey = 'your_anon_key_here';
```

### Authentication Settings
- Email confirmation: Configurable in Supabase dashboard
- Password requirements: Minimum 6 characters
- Username validation: 3-24 characters, alphanumeric and underscore only

## State Management

The authentication state is managed using BLoC pattern:

### Auth States
- `AuthInitial`: Initial state
- `AuthLoading`: Loading operations
- `AuthAuthenticated`: User is signed in
- `AuthUnauthenticated`: User is signed out
- `AuthError`: Error occurred
- `AuthSignUpSuccess`: Registration successful (email confirmation required)
- `AuthPasswordResetSent`: Password reset email sent

### Auth Events
- `AuthCheckRequested`: Check current authentication status
- `AuthSignInRequested`: Sign in with credentials
- `AuthSignUpRequested`: Register new user
- `AuthPasswordResetRequested`: Request password reset
- `AuthSignOutRequested`: Sign out user

## Validation

### Email Validation
- Required field
- Valid email format using regex

### Password Validation
- Required field
- Minimum 6 characters
- Password confirmation must match

### Username Validation
- Required field
- 3-24 characters
- Alphanumeric characters and underscore only
- Unique validation (handled by Supabase)

## Error Handling

The system handles various error scenarios:

### Authentication Errors
- Invalid credentials
- User not found
- Email already exists
- Username already exists
- Weak password
- Too many requests
- Network errors
- Session expired

### User Feedback
- Snackbar messages for errors and success
- Loading indicators during operations
- Form validation messages
- Localized error messages (Arabic/English)

## Localization

All authentication screens support Arabic and English:

### Supported Languages
- Arabic (ar): Right-to-left layout
- English (en): Left-to-right layout

### Localized Elements
- All text labels and messages
- Error messages
- Success messages
- Form hints and placeholders

## Navigation Flow

```
SplashScreen
    ↓
AuthCheck
    ↓
┌─────────────────┐    ┌──────────────────┐
│   LoginScreen   │ ←→ │ RegisterScreen   │
└─────────────────┘    └──────────────────┘
    ↓                           ↑
┌─────────────────┐             │
│ForgotPassword   │─────────────┘
│Screen           │
└─────────────────┘
    ↓
┌─────────────────┐
│   HomeScreen    │
└─────────────────┘
```

## Testing

### Unit Tests
- Use case testing
- Repository testing
- BLoC testing
- Validation testing

### Widget Tests
- Screen rendering
- Form validation
- User interactions
- Navigation

### Integration Tests
- Authentication flow
- Error scenarios
- State persistence

## Security Considerations

1. **Never expose service keys** in client-side code
2. **Use Row Level Security** policies in Supabase
3. **Validate all inputs** on both client and server
4. **Implement proper session management**
5. **Use HTTPS** for all communications
6. **Handle sensitive data** appropriately

## Future Enhancements

1. **Biometric Authentication**
   - Fingerprint/Face ID support
   - Local authentication

2. **Multi-Factor Authentication**
   - SMS verification
   - TOTP support

3. **Social Providers**
   - Facebook, Twitter, GitHub
   - Custom OAuth providers

4. **Advanced Security**
   - Device registration
   - Suspicious activity detection
   - Account lockout policies

## Dependencies

```yaml
dependencies:
  supabase_flutter: ^2.5.6
  flutter_bloc: ^8.1.6
  dartz: ^0.10.1
  equatable: ^2.0.5
  get_it: ^7.6.7
```

## Getting Started

1. **Configure Supabase**
   - Create a Supabase project
   - Set up authentication providers
   - Configure RLS policies

2. **Update Configuration**
   - Add your Supabase URL and anon key
   - Configure authentication settings

3. **Run the App**
   ```bash
   flutter pub get
   flutter run
   ```

4. **Test Authentication**
   - Try registering a new user
   - Test login functionality
   - Test password reset flow
