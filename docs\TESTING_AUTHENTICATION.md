# Testing Authentication System

## Manual Testing Guide

### Prerequisites
1. Ensure the app is running (`flutter run`)
2. Have access to an email account for testing
3. Supabase project is properly configured

### Test Scenarios

#### 1. Initial App Launch
**Expected Behavior:**
- App shows splash screen for 3 seconds
- Automatically navigates to login screen (since no user is authenticated)

**Steps:**
1. Launch the app
2. Wait for splash screen animation
3. Verify navigation to login screen

#### 2. Login Screen Testing

**Test Case 2.1: UI Elements**
- ✅ App logo is displayed
- ✅ "Welcome Back" title and subtitle
- ✅ Email field with proper label and hint
- ✅ Password field with visibility toggle
- ✅ "Forgot Password?" link
- ✅ "Sign In" button
- ✅ Social login buttons (Google, Apple on iOS)
- ✅ "Don't have an account? Sign Up" link

**Test Case 2.2: Form Validation**
1. Try submitting empty form
   - Should show validation errors
2. Enter invalid email format
   - Should show "Please enter a valid email" error
3. Enter valid email but short password (< 6 chars)
   - Should show "Password must be at least 6 characters" error
4. Enter valid credentials
   - Form should submit successfully

**Test Case 2.3: Login Functionality**
1. Enter valid test credentials:
   - Email: `<EMAIL>`
   - Password: `password123`
2. Tap "Sign In" button
3. Verify loading state is shown
4. If credentials are valid, should navigate to home screen
5. If invalid, should show error message

#### 3. Registration Screen Testing

**Test Case 3.1: Navigation**
1. From login screen, tap "Sign Up" link
2. Should navigate to registration screen
3. Back button should return to login screen

**Test Case 3.2: Form Fields**
- ✅ Full Name field
- ✅ Username field  
- ✅ Email field
- ✅ Password field
- ✅ Confirm Password field
- ✅ All fields have proper validation

**Test Case 3.3: Validation Testing**
1. **Full Name Validation:**
   - Empty: Should show "Full name is required"
   - Less than 2 chars: Should show error
   - Valid name: Should pass

2. **Username Validation:**
   - Empty: Should show "Username is required"
   - Less than 3 chars: Should show validation error
   - More than 24 chars: Should show validation error
   - Invalid characters (spaces, special chars): Should show error
   - Valid username: Should pass

3. **Email Validation:**
   - Same as login screen validation

4. **Password Validation:**
   - Same as login screen validation

5. **Confirm Password:**
   - Empty: Should show "Password is required"
   - Doesn't match password: Should show "Passwords do not match"
   - Matches password: Should pass

**Test Case 3.4: Registration Flow**
1. Fill all fields with valid data:
   ```
   Full Name: John Doe
   Username: johndoe123
   Email: <EMAIL>
   Password: password123
   Confirm Password: password123
   ```
2. Tap "Sign Up" button
3. Verify loading state
4. Should show success message or navigate based on email confirmation settings

#### 4. Forgot Password Screen Testing

**Test Case 4.1: Navigation**
1. From login screen, tap "Forgot Password?" link
2. Should navigate to forgot password screen
3. Back button should return to login screen

**Test Case 4.2: Password Reset Flow**
1. Enter valid email address
2. Tap "Send Reset Link" button
3. Should show loading state
4. Should show success message and email confirmation
5. Check email for reset link

**Test Case 4.3: Resend Functionality**
1. After successful reset request
2. Tap "Resend Confirmation" button
3. Should send another reset email

#### 5. Error Handling Testing

**Test Case 5.1: Network Errors**
1. Disconnect internet
2. Try to login/register
3. Should show appropriate network error message

**Test Case 5.2: Invalid Credentials**
1. Enter wrong email/password combination
2. Should show "Invalid credentials" error

**Test Case 5.3: Existing User Registration**
1. Try to register with existing email
2. Should show "Email already in use" error

#### 6. Social Login Testing

**Test Case 6.1: Google Sign In**
1. Tap "Sign in with Google" button
2. Should open Google OAuth flow
3. Complete authentication
4. Should return to app and authenticate user

**Test Case 6.2: Apple Sign In (iOS only)**
1. On iOS device, tap "Sign in with Apple" button
2. Should open Apple ID authentication
3. Complete authentication
4. Should return to app and authenticate user

#### 7. Authentication State Testing

**Test Case 7.1: Session Persistence**
1. Login successfully
2. Close and reopen app
3. Should remain logged in and go to home screen

**Test Case 7.2: Logout Functionality**
1. From home screen, tap logout button
2. Should sign out user
3. Should navigate to login screen
4. Reopening app should show login screen

#### 8. Localization Testing

**Test Case 8.1: Arabic Language**
1. Change device language to Arabic
2. Restart app
3. Verify all text is in Arabic
4. Verify RTL layout is applied
5. Test all authentication flows

**Test Case 8.2: English Language**
1. Change device language to English
2. Restart app
3. Verify all text is in English
4. Verify LTR layout is applied

### Automated Testing

#### Unit Tests
Run unit tests for authentication logic:
```bash
flutter test test/domain/usecases/auth/
flutter test test/data/repositories/auth_repository_test.dart
flutter test test/presentation/providers/auth/auth_bloc_test.dart
```

#### Widget Tests
Run widget tests for authentication screens:
```bash
flutter test test/presentation/screens/auth/
flutter test test/shared/widgets/auth_*_test.dart
```

#### Integration Tests
Run integration tests for complete authentication flows:
```bash
flutter test integration_test/auth_flow_test.dart
```

### Performance Testing

#### Load Time Testing
1. Measure app startup time
2. Measure login response time
3. Measure registration response time
4. Verify smooth animations

#### Memory Usage
1. Monitor memory usage during authentication flows
2. Check for memory leaks
3. Verify proper disposal of controllers and streams

### Security Testing

#### Input Validation
1. Test SQL injection attempts in form fields
2. Test XSS attempts in text inputs
3. Verify proper input sanitization

#### Session Security
1. Verify JWT tokens are properly handled
2. Test session expiration handling
3. Verify secure storage of credentials

### Accessibility Testing

#### Screen Reader Support
1. Enable screen reader (TalkBack/VoiceOver)
2. Navigate through authentication screens
3. Verify all elements are properly labeled
4. Test form submission with screen reader

#### Keyboard Navigation
1. Test tab navigation through forms
2. Verify proper focus management
3. Test form submission with keyboard only

### Browser Testing (Web)

#### Cross-Browser Compatibility
1. Test on Chrome
2. Test on Firefox
3. Test on Safari
4. Test on Edge

#### Responsive Design
1. Test on different screen sizes
2. Verify mobile responsiveness
3. Test tablet layouts

### Reporting Issues

When reporting authentication issues, include:

1. **Device Information:**
   - Platform (iOS/Android/Web)
   - OS version
   - Device model
   - Browser (for web)

2. **Steps to Reproduce:**
   - Detailed step-by-step instructions
   - Expected vs actual behavior
   - Screenshots/videos if applicable

3. **Error Information:**
   - Error messages
   - Console logs
   - Network request details

4. **Environment:**
   - App version
   - Supabase configuration
   - Network conditions

### Test Data

Use these test accounts for testing:

```
Test User 1:
Email: <EMAIL>
Password: testpass123
Username: testuser1

Test User 2:
Email: <EMAIL>  
Password: testpass123
Username: testuser2
```

**Note:** Create these accounts in your Supabase project for consistent testing.
