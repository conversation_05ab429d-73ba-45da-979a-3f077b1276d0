import 'package:dartz/dartz.dart';
import '../../core/error/failures.dart';
import '../entities/auth_entity.dart';
import '../entities/user_entity.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Sign in with email and password
  Future<Either<Failure, AuthEntity>> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Sign up with email and password
  Future<Either<Failure, AuthEntity>> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String username,
  });

  /// Sign in with magic link (OTP)
  Future<Either<Failure, void>> signInWithMagicLink({
    required String email,
    String? redirectTo,
  });

  /// Verify OTP for magic link or phone authentication
  Future<Either<Failure, AuthEntity>> verifyOTP({
    required String email,
    required String token,
    required String type, // 'magiclink', 'signup', 'recovery'
  });

  /// Reset password
  Future<Either<Failure, void>> resetPassword({
    required String email,
    String? redirectTo,
  });

  /// Update password
  Future<Either<Failure, void>> updatePassword({
    required String newPassword,
    required String accessToken,
  });

  /// Sign out
  Future<Either<Failure, void>> signOut();

  /// Get current session
  Future<Either<Failure, AuthEntity?>> getCurrentSession();

  /// Refresh session
  Future<Either<Failure, AuthEntity>> refreshSession({
    required String refreshToken,
  });

  /// Get current user
  Future<Either<Failure, UserEntity?>> getCurrentUser();

  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();

  /// Listen to authentication state changes
  Stream<AuthEntity> get authStateChanges;

  /// Sign in with OAuth provider (Google, Apple, etc.)
  Future<Either<Failure, AuthEntity>> signInWithOAuth({
    required String provider,
    String? redirectTo,
  });

  /// Delete user account
  Future<Either<Failure, void>> deleteAccount();

  /// Resend email confirmation
  Future<Either<Failure, void>> resendEmailConfirmation({
    required String email,
  });

  /// Update user metadata
  Future<Either<Failure, UserEntity>> updateUserMetadata({
    required Map<String, dynamic> metadata,
  });
}
