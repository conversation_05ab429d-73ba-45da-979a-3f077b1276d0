import 'package:equatable/equatable.dart';

/// Base class for authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check authentication status
class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

/// Event to sign in with email and password
class AuthSignInRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthSignInRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

/// Event to sign up with email and password
class AuthSignUpRequested extends AuthEvent {
  final String email;
  final String password;
  final String fullName;
  final String username;

  const AuthSignUpRequested({
    required this.email,
    required this.password,
    required this.fullName,
    required this.username,
  });

  @override
  List<Object> get props => [email, password, fullName, username];
}

/// Event to sign in with magic link
class AuthMagicLinkRequested extends AuthEvent {
  final String email;
  final String? redirectTo;

  const AuthMagicLinkRequested({
    required this.email,
    this.redirectTo,
  });

  @override
  List<Object?> get props => [email, redirectTo];
}

/// Event to verify OTP
class AuthOTPVerificationRequested extends AuthEvent {
  final String email;
  final String token;
  final String type;

  const AuthOTPVerificationRequested({
    required this.email,
    required this.token,
    required this.type,
  });

  @override
  List<Object> get props => [email, token, type];
}

/// Event to reset password
class AuthPasswordResetRequested extends AuthEvent {
  final String email;
  final String? redirectTo;

  const AuthPasswordResetRequested({
    required this.email,
    this.redirectTo,
  });

  @override
  List<Object?> get props => [email, redirectTo];
}

/// Event to update password
class AuthPasswordUpdateRequested extends AuthEvent {
  final String newPassword;
  final String accessToken;

  const AuthPasswordUpdateRequested({
    required this.newPassword,
    required this.accessToken,
  });

  @override
  List<Object> get props => [newPassword, accessToken];
}

/// Event to sign out
class AuthSignOutRequested extends AuthEvent {
  const AuthSignOutRequested();
}

/// Event to refresh session
class AuthSessionRefreshRequested extends AuthEvent {
  final String refreshToken;

  const AuthSessionRefreshRequested({
    required this.refreshToken,
  });

  @override
  List<Object> get props => [refreshToken];
}

/// Event to sign in with OAuth provider
class AuthOAuthSignInRequested extends AuthEvent {
  final String provider;
  final String? redirectTo;

  const AuthOAuthSignInRequested({
    required this.provider,
    this.redirectTo,
  });

  @override
  List<Object?> get props => [provider, redirectTo];
}

/// Event to delete account
class AuthAccountDeletionRequested extends AuthEvent {
  const AuthAccountDeletionRequested();
}

/// Event to resend email confirmation
class AuthEmailConfirmationResendRequested extends AuthEvent {
  final String email;

  const AuthEmailConfirmationResendRequested({
    required this.email,
  });

  @override
  List<Object> get props => [email];
}

/// Event when authentication state changes externally
class AuthStateChanged extends AuthEvent {
  final bool isAuthenticated;
  final String? userId;
  final String? email;

  const AuthStateChanged({
    required this.isAuthenticated,
    this.userId,
    this.email,
  });

  @override
  List<Object?> get props => [isAuthenticated, userId, email];
}

/// Event to clear authentication errors
class AuthErrorCleared extends AuthEvent {
  const AuthErrorCleared();
}
