# Sijilli App - Project Architecture

## Overview
Sijilli (سجلي) is a Flutter application for appointment and article management with social features. The app follows Clean Architecture principles with a well-structured, scalable codebase.

## Features
- **User Management**: Registration, authentication, profiles, and social following
- **Appointment Management**: Create, edit, and manage appointments with Hijri calendar support
- **Article Management**: Create and share articles with collaboration features
- **Social Features**: Follow users, repost appointments, notifications
- **Organization Support**: Verified organization profiles with special features
- **Multi-language Support**: Arabic (primary) and English
- **Responsive Design**: Optimized for mobile devices

## Project Structure

```
lib/
├── core/                           # Core application configuration
│   ├── app/                        # App initialization and configuration
│   │   └── sijilli_app.dart        # Main app widget
│   ├── config/                     # App configuration
│   │   └── app_config.dart         # Configuration settings
│   ├── constants/                  # Application constants
│   │   └── app_constants.dart      # App-wide constants
│   ├── error/                      # Error handling
│   │   └── failures.dart           # Failure classes
│   └── theme/                      # App theming
│       ├── app_theme.dart          # Theme configuration
│       ├── app_colors.dart         # Color palette
│       └── app_text_styles.dart    # Text styles
│
├── data/                           # Data layer
│   ├── datasources/                # Data sources (API, local storage)
│   ├── models/                     # Data models
│   │   ├── user_model.dart         # User data model
│   │   └── appointment_model.dart  # Appointment data model
│   └── repositories/               # Repository implementations
│
├── domain/                         # Business logic layer
│   ├── entities/                   # Business entities
│   │   ├── user_entity.dart        # User entity
│   │   ├── appointment_entity.dart # Appointment entity
│   │   └── article_entity.dart     # Article entity
│   ├── repositories/               # Repository interfaces
│   │   └── user_repository.dart    # User repository interface
│   └── usecases/                   # Use cases
│       ├── usecase.dart            # Base use case
│       └── get_current_user.dart   # Get current user use case
│
├── presentation/                   # UI layer
│   ├── screens/                    # App screens
│   │   └── splash/                 # Splash screen
│   │       └── splash_screen.dart  # Splash screen implementation
│   ├── widgets/                    # Reusable widgets
│   └── providers/                  # State management (Bloc/Provider)
│
├── shared/                         # Shared utilities and components
│   ├── utils/                      # Utility functions
│   │   ├── date_utils.dart         # Date/time utilities
│   │   └── validators.dart         # Input validation
│   └── widgets/                    # Shared widgets
│       ├── custom_button.dart      # Custom button widget
│       └── custom_text_field.dart  # Custom text field widget
│
└── main.dart                       # App entry point
```

## Architecture Principles

### Clean Architecture
The project follows Clean Architecture principles with clear separation of concerns:

1. **Presentation Layer**: UI components, state management, and user interactions
2. **Domain Layer**: Business logic, entities, and use cases
3. **Data Layer**: Data sources, models, and repository implementations

### Key Benefits
- **Testability**: Each layer can be tested independently
- **Maintainability**: Clear separation makes code easier to maintain
- **Scalability**: Easy to add new features without affecting existing code
- **Flexibility**: Easy to change data sources or UI frameworks

## Design Patterns

### Repository Pattern
- Abstracts data access logic
- Provides a clean API for data operations
- Enables easy testing with mock implementations

### Use Case Pattern
- Encapsulates business logic
- Single responsibility for each use case
- Easy to test and maintain

### Dependency Injection
- Loose coupling between components
- Easy to mock dependencies for testing
- Better code organization

## State Management
The app uses a combination of state management solutions:
- **Provider/Riverpod**: For simple state management
- **Bloc**: For complex business logic and state transitions
- **Local State**: For UI-specific state (StatefulWidget)

## Database Schema
The app uses Supabase (PostgreSQL) with the following main tables:
- `users`: User profiles and authentication
- `appointments`: Appointment data with Gregorian/Hijri dates
- `articles`: Article content and metadata
- `notifications`: User notifications
- `follows`: User following relationships
- `organizations`: Organization-specific data

## Localization
- Primary language: Arabic (RTL)
- Secondary language: English (LTR)
- Dynamic text direction based on content
- Date formatting for both Gregorian and Hijri calendars

## Theme System
- Material Design 3 (Material You)
- Islamic Green color scheme
- Support for light and dark themes
- Consistent typography with Arabic font support
- Responsive design for different screen sizes

## Development Guidelines

### Code Style
- Follow Dart/Flutter best practices
- Use meaningful variable and function names
- Add documentation for public APIs
- Keep functions small and focused

### File Organization
- Group related files in directories
- Use descriptive file names
- Follow the established folder structure
- Separate concerns appropriately

### Testing Strategy
- Unit tests for business logic (domain layer)
- Widget tests for UI components
- Integration tests for complete user flows
- Mock external dependencies

### Performance Considerations
- Lazy loading for large lists
- Image caching and optimization
- Efficient state management
- Minimal rebuilds

## Getting Started

### Prerequisites
- Flutter SDK (latest stable version)
- Dart SDK
- Android Studio / VS Code
- Supabase account for backend services

### Setup
1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Configure Supabase credentials in `app_config.dart`
4. Run `flutter run` to start the app

### Dependencies
Key dependencies include:
- `flutter_bloc`: State management
- `dartz`: Functional programming utilities
- `equatable`: Value equality
- `intl`: Internationalization
- `supabase_flutter`: Backend services

## Contributing
1. Follow the established architecture patterns
2. Write tests for new features
3. Update documentation as needed
4. Follow the code style guidelines
5. Create meaningful commit messages

## Future Enhancements
- Push notifications
- Offline support
- Advanced calendar features
- File attachments
- Video calls integration
- Analytics and reporting
