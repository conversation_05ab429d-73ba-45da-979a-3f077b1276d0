# Sijilli App - Localization Guide

## Overview
The Sijilli app supports full localization with Arabic (primary) and English (secondary) languages. The implementation uses Flutter's built-in internationalization (i18n) system with ARB (Application Resource Bundle) files.

## Supported Languages
- **Arabic (ar_SA)** - Primary language, RTL layout
- **English (en_US)** - Secondary language, LTR layout

## Architecture

### Core Components
1. **ARB Files** - Translation resources in `lib/l10n/`
2. **LocalizationService** - Language management service
3. **LocalizationProvider** - State management for language switching
4. **Generated Classes** - Auto-generated localization classes
5. **Utility Classes** - Helper functions for localization

### File Structure
```
lib/
├── l10n/
│   ├── app_en.arb              # English translations
│   ├── app_ar.arb              # Arabic translations
│   ├── header.txt              # Generated file header
│   └── generated/              # Auto-generated files
│       └── app_localizations.dart
├── core/
│   └── localization/
│       └── localization_service.dart
├── presentation/
│   └── providers/
│       └── localization/
│           └── localization_provider.dart
└── shared/
    ├── utils/
    │   └── localization_utils.dart
    └── widgets/
        └── language_selector.dart
```

## Configuration Files

### l10n.yaml
```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/l10n/generated
nullable-getter: false
synthetic-package: false
```

### pubspec.yaml
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

flutter:
  generate: true
```

## Usage

### Basic Usage
```dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'l10n/generated/app_localizations.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
      ],
      home: MyHomePage(),
    );
  }
}
```

### Using Translations in Widgets
```dart
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Column(
      children: [
        Text(l10n.appName),
        Text(l10n.welcome),
        ElevatedButton(
          onPressed: () {},
          child: Text(l10n.save),
        ),
      ],
    );
  }
}
```

### Language Switching
```dart
// Using LocalizationProvider
Consumer<LocalizationProvider>(
  builder: (context, provider, child) {
    return ElevatedButton(
      onPressed: () async {
        await provider.switchLanguage();
      },
      child: Text(provider.currentLanguageName),
    );
  },
)

// Direct service usage
final service = LocalizationService.instance;
await service.setLocale(Locale('ar', 'SA'));
```

## Key Features

### 1. Automatic Text Direction
The app automatically switches between RTL (Arabic) and LTR (English) based on the selected language.

```dart
// Automatic direction handling
Directionality(
  textDirection: localizationProvider.textDirection,
  child: child,
)
```

### 2. Language Selection UI
Multiple UI components for language selection:

- **LanguageSelector** - Full language selection widget
- **LanguageToggleButton** - Quick toggle button
- **LanguageSelectionDialog** - Modal dialog for selection

### 3. Localized Formatting
Utility functions for locale-specific formatting:

```dart
// Date formatting
LocalizationUtils.formatDate(DateTime.now(), locale);

// Number formatting
LocalizationUtils.formatNumber(1234.56, locale);

// Relative time
LocalizationUtils.getRelativeTime(dateTime, locale);

// Currency formatting
LocalizationUtils.formatCurrency(100.0, locale);
```

### 4. Arabic Number Support
Automatic conversion between Arabic and English numerals:

```dart
// Convert to Arabic numbers
LocalizationUtils.localizeNumbers("123", Locale('ar'));
// Result: "١٢٣"

// Convert to English numbers
LocalizationUtils.convertToEnglishNumbers("١٢٣");
// Result: "123"
```

### 5. Plural Forms
Support for Arabic and English plural forms:

```dart
// Arabic plurals (singular, dual, plural)
LocalizationUtils.getArabicPlural(count, "كتاب", "كتابان", "كتب");

// English plurals
LocalizationUtils.getEnglishPlural(count, "book", "books");
```

## Adding New Translations

### 1. Update ARB Files
Add new keys to both `app_en.arb` and `app_ar.arb`:

**app_en.arb:**
```json
{
  "newFeature": "New Feature",
  "@newFeature": {
    "description": "Label for new feature"
  }
}
```

**app_ar.arb:**
```json
{
  "newFeature": "ميزة جديدة"
}
```

### 2. Generate Code
Run the code generation command:
```bash
flutter gen-l10n
```

### 3. Use in Code
```dart
Text(AppLocalizations.of(context)!.newFeature)
```

## Best Practices

### 1. Key Naming
- Use camelCase for keys
- Use descriptive names
- Group related keys with prefixes

```json
{
  "loginTitle": "Login",
  "loginEmail": "Email",
  "loginPassword": "Password",
  "loginButton": "Sign In"
}
```

### 2. Context Descriptions
Always add descriptions for translators:

```json
{
  "save": "Save",
  "@save": {
    "description": "Button text to save changes"
  }
}
```

### 3. Parameterized Strings
Use placeholders for dynamic content:

```json
{
  "welcomeUser": "Welcome, {name}!",
  "@welcomeUser": {
    "description": "Welcome message with user name",
    "placeholders": {
      "name": {
        "type": "String",
        "example": "Ahmed"
      }
    }
  }
}
```

Usage:
```dart
Text(AppLocalizations.of(context)!.welcomeUser("Ahmed"))
```

### 4. Date and Time Formatting
Use locale-aware formatting:

```dart
// Good
LocalizationUtils.formatDate(date, locale)

// Avoid
DateFormat('dd/MM/yyyy').format(date)
```

### 5. RTL Layout Considerations
- Use `EdgeInsets.symmetric()` instead of directional padding
- Use `MainAxisAlignment.start` instead of `left`
- Test UI in both RTL and LTR modes

## Testing Localization

### 1. Manual Testing
- Switch languages in settings
- Verify text direction changes
- Check all screens for proper layout
- Test with long Arabic text

### 2. Automated Testing
```dart
testWidgets('should display Arabic text correctly', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      locale: Locale('ar', 'SA'),
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      home: MyWidget(),
    ),
  );
  
  expect(find.text('مرحباً'), findsOneWidget);
});
```

### 3. Pseudo-localization
Use pseudo-localization for testing:
- Add extra characters to test UI overflow
- Use special characters to test encoding
- Test with very long strings

## Common Issues and Solutions

### 1. Missing Translations
**Problem:** Key not found in ARB file
**Solution:** Add the key to both language files and regenerate

### 2. Layout Issues in RTL
**Problem:** UI elements misaligned in Arabic
**Solution:** Use proper Flutter RTL widgets and test thoroughly

### 3. Font Issues
**Problem:** Arabic text not displaying correctly
**Solution:** Ensure proper Arabic fonts are included

### 4. Number Formatting
**Problem:** Numbers not displaying in correct format
**Solution:** Use LocalizationUtils for number formatting

## Performance Considerations

1. **Lazy Loading:** Translations are loaded on demand
2. **Caching:** LocalizationService caches the current locale
3. **Memory:** ARB files are loaded into memory efficiently
4. **Build Time:** Code generation happens at build time

## Future Enhancements

1. **Additional Languages:** Easy to add more languages
2. **Regional Variants:** Support for different Arabic dialects
3. **Dynamic Loading:** Load translations from server
4. **Translation Management:** Integration with translation services
5. **Accessibility:** Enhanced support for screen readers
