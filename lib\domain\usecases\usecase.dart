import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../core/error/failures.dart';

/// Base class for all use cases
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// Use case that doesn't require parameters
class NoParams extends Equatable {
  const NoParams();

  @override
  List<Object> get props => [];
}

/// Parameters for paginated requests
class PaginationParams extends Equatable {
  final int page;
  final int limit;

  const PaginationParams({this.page = 1, this.limit = 20});

  @override
  List<Object> get props => [page, limit];
}

/// Parameters for search requests
class SearchParams extends Equatable {
  final String query;
  final int page;
  final int limit;

  const SearchParams({required this.query, this.page = 1, this.limit = 20});

  @override
  List<Object> get props => [query, page, limit];
}

/// Parameters for ID-based requests
class IdParams extends Equatable {
  final String id;

  const IdParams({required this.id});

  @override
  List<Object> get props => [id];
}

/// Parameters for date range requests
class DateRangeParams extends Equatable {
  final DateTime startDate;
  final DateTime endDate;
  final int page;
  final int limit;

  const DateRangeParams({
    required this.startDate,
    required this.endDate,
    this.page = 1,
    this.limit = 20,
  });

  @override
  List<Object> get props => [startDate, endDate, page, limit];
}
