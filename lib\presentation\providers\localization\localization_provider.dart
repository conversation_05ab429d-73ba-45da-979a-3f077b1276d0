import 'package:flutter/material.dart';
import '../../../core/localization/localization_service.dart';

/// Provider for managing app localization state
class LocalizationProvider extends ChangeNotifier {
  final LocalizationService _localizationService;
  
  Locale _currentLocale;
  
  LocalizationProvider(this._localizationService)
      : _currentLocale = _localizationService.getCurrentLocale();
  
  /// Get current locale
  Locale get currentLocale => _currentLocale;
  
  /// Get current language code
  String get currentLanguageCode => _currentLocale.languageCode;
  
  /// Get text direction for current locale
  TextDirection get textDirection => 
      _localizationService.getTextDirection(_currentLocale);
  
  /// Check if current locale is RTL
  bool get isRTL => _localizationService.isRTL(_currentLocale);
  
  /// Get supported locales
  List<Locale> get supportedLocales => 
      _localizationService.getSupportedLocales();
  
  /// Get language options for UI
  List<LanguageOption> get languageOptions => 
      _localizationService.getLanguageOptions();
  
  /// Set locale
  Future<void> setLocale(Locale locale) async {
    if (_currentLocale == locale) return;
    
    if (_localizationService.isLocaleSupported(locale)) {
      await _localizationService.setLocale(locale);
      _currentLocale = locale;
      notifyListeners();
    }
  }
  
  /// Set locale by language code
  Future<void> setLanguage(String languageCode) async {
    final locale = _getLocaleFromLanguageCode(languageCode);
    await setLocale(locale);
  }
  
  /// Switch to next available language
  Future<void> switchLanguage() async {
    final nextLocale = await _localizationService.switchLanguage();
    _currentLocale = nextLocale;
    notifyListeners();
  }
  
  /// Reset to default language
  Future<void> resetToDefault() async {
    final defaultLocale = await _localizationService.resetToDefault();
    _currentLocale = defaultLocale;
    notifyListeners();
  }
  
  /// Get language name for display
  String getLanguageName(String languageCode, {String? displayLanguage}) {
    return _localizationService.getLanguageName(
      languageCode,
      displayLanguage: displayLanguage,
    );
  }
  
  /// Get current language name
  String get currentLanguageName => 
      getLanguageName(currentLanguageCode, displayLanguage: currentLanguageCode);
  
  /// Check if locale is current
  bool isCurrentLocale(Locale locale) {
    return _currentLocale.languageCode == locale.languageCode;
  }
  
  /// Check if language code is current
  bool isCurrentLanguage(String languageCode) {
    return _currentLocale.languageCode == languageCode;
  }
  
  /// Get locale from language code
  Locale _getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return const Locale('ar', 'SA');
      case 'en':
        return const Locale('en', 'US');
      default:
        return const Locale('ar', 'SA');
    }
  }
}
