import 'package:supabase_flutter/supabase_flutter.dart';
import '../localization/localization_service.dart';

/// Application configuration and initialization
class AppConfig {
  static const String appName = 'Sijilli';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const String baseUrl = 'https://api.sijilli.com';
  static const String supabaseUrl = 'https://czofsbnnrwhqfolgqmkz.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN6b2ZzYm5ucndocWZvbGdxbWt6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxOTM5ODUsImV4cCI6MjA2MTc2OTk4NX0.Hlj6LWw0bW38_ITc0FmLR5z4SAIwkzk9GzKF1MhuIHU';

  // App Settings
  static const int sessionTimeoutMinutes = 30;
  static const int maxFileUploadSizeMB = 10;
  static const int notificationRefreshIntervalSeconds = 30;

  // Supported Languages
  static const List<String> supportedLanguages = ['ar', 'en'];
  static const String defaultLanguage = 'ar';

  // Theme Settings
  static const String defaultTheme = 'light';
  static const String defaultFontFamily = 'sans-serif';

  // Calendar Settings
  static const int defaultHijriCorrection = 0;

  /// Initialize app configuration
  static Future<void> initialize() async {
    // Initialize any required services here
    // e.g., Firebase, Supabase, local storage, etc.

    // Initialize localization service
    await _initializeLocalization();

    // Initialize Supabase
    await _initializeSupabase();

    // TODO: Initialize local storage
    // TODO: Initialize notification services
    // TODO: Initialize analytics
  }

  /// Initialize Supabase
  static Future<void> _initializeSupabase() async {
    try {
      await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);
    } catch (e) {
      // Handle Supabase initialization error
      if (isDebug) {
        print('Failed to initialize Supabase: $e');
      }
    }
  }

  /// Initialize localization service
  static Future<void> _initializeLocalization() async {
    try {
      final localizationService = LocalizationService.instance;
      await localizationService.initialize();
    } catch (e) {
      // Handle localization initialization error
      if (isDebug) {
        print('Failed to initialize localization: $e');
      }
    }
  }

  /// Check if the app is in debug mode
  static bool get isDebug {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// Get environment-specific configuration
  static String get environment {
    if (isDebug) {
      return 'development';
    }
    return 'production';
  }
}
