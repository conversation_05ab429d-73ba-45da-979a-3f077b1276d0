import 'package:dartz/dartz.dart';
import '../../../core/error/failures.dart';
import '../../repositories/auth_repository.dart';
import '../usecase.dart';

/// Use case for password reset
class ResetPassword implements UseCase<void, ResetPasswordParams> {
  final AuthRepository repository;

  const ResetPassword(this.repository);

  @override
  Future<Either<Failure, void>> call(ResetPasswordParams params) async {
    return await repository.resetPassword(
      email: params.email,
      redirectTo: params.redirectTo,
    );
  }
}

/// Parameters for reset password use case
class ResetPasswordParams {
  final String email;
  final String? redirectTo;

  const ResetPasswordParams({
    required this.email,
    this.redirectTo,
  });
}
