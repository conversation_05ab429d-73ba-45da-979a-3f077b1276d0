import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../../core/localization/localization_service.dart';
import '../../presentation/providers/localization/localization_provider.dart';

/// Widget for selecting app language
class LanguageSelector extends StatelessWidget {
  final bool showTitle;
  final bool isCompact;
  final VoidCallback? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.showTitle = true,
    this.isCompact = false,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationProvider>(
      builder: (context, localizationProvider, child) {
        if (isCompact) {
          return _buildCompactSelector(context, localizationProvider);
        }
        return _buildFullSelector(context, localizationProvider);
      },
    );
  }

  Widget _buildFullSelector(BuildContext context, LocalizationProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showTitle) ...[
          Text(
            'اللغة / Language',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 12),
        ],
        ...provider.languageOptions.map((option) {
          final isSelected = provider.isCurrentLanguage(option.code);
          return _buildLanguageOption(
            context,
            option,
            isSelected,
            () => _selectLanguage(context, provider, option),
          );
        }),
      ],
    );
  }

  Widget _buildCompactSelector(BuildContext context, LocalizationProvider provider) {
    return PopupMenuButton<LanguageOption>(
      icon: Icon(
        Icons.language,
        color: AppColors.onSurface,
      ),
      tooltip: 'تغيير اللغة / Change Language',
      onSelected: (option) => _selectLanguage(context, provider, option),
      itemBuilder: (context) {
        return provider.languageOptions.map((option) {
          final isSelected = provider.isCurrentLanguage(option.code);
          return PopupMenuItem<LanguageOption>(
            value: option,
            child: Row(
              children: [
                if (isSelected)
                  const Icon(
                    Icons.check,
                    color: AppColors.primary,
                    size: 20,
                  )
                else
                  const SizedBox(width: 20),
                const SizedBox(width: 12),
                Text(
                  option.nativeName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: isSelected ? AppColors.primary : AppColors.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    LanguageOption option,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primary : AppColors.outline,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            color: isSelected 
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              // Language flag or icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.primary : AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  _getLanguageIcon(option.code),
                  color: isSelected ? AppColors.onPrimary : AppColors.onSurfaceVariant,
                  size: 18,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Language name
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.nativeName,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: isSelected ? AppColors.primary : AppColors.onSurface,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    if (option.name != option.nativeName) ...[
                      const SizedBox(height: 2),
                      Text(
                        option.name,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Selection indicator
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: AppColors.primary,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getLanguageIcon(String languageCode) {
    switch (languageCode) {
      case 'ar':
        return Icons.language; // You can use a custom Arabic icon
      case 'en':
        return Icons.language; // You can use a custom English icon
      default:
        return Icons.language;
    }
  }

  Future<void> _selectLanguage(
    BuildContext context,
    LocalizationProvider provider,
    LanguageOption option,
  ) async {
    if (!provider.isCurrentLanguage(option.code)) {
      await provider.setLocale(option.locale);
      onLanguageChanged?.call();
      
      // Show confirmation snackbar
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              option.code == 'ar' 
                  ? 'تم تغيير اللغة إلى العربية'
                  : 'Language changed to English',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onPrimary,
              ),
            ),
            backgroundColor: AppColors.primary,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
          ),
        );
      }
    }
  }
}

/// Quick language toggle button
class LanguageToggleButton extends StatelessWidget {
  final VoidCallback? onLanguageChanged;

  const LanguageToggleButton({
    super.key,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<LocalizationProvider>(
      builder: (context, localizationProvider, child) {
        return IconButton(
          icon: Icon(
            Icons.translate,
            color: AppColors.onSurface,
          ),
          tooltip: localizationProvider.currentLanguageCode == 'ar'
              ? 'Switch to English'
              : 'التبديل إلى العربية',
          onPressed: () async {
            await localizationProvider.switchLanguage();
            onLanguageChanged?.call();
          },
        );
      },
    );
  }
}

/// Language selection dialog
class LanguageSelectionDialog extends StatelessWidget {
  const LanguageSelectionDialog({super.key});

  static Future<void> show(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const LanguageSelectionDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'اختر اللغة / Select Language',
        style: AppTextStyles.titleLarge,
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: LanguageSelector(
          showTitle: false,
          onLanguageChanged: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'إغلاق / Close',
            style: AppTextStyles.labelLarge.copyWith(
              color: AppColors.primary,
            ),
          ),
        ),
      ],
    );
  }
}
