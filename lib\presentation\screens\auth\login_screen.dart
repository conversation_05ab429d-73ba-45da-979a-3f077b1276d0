import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../shared/widgets/auth_text_field.dart';
import '../../../shared/widgets/auth_button.dart';
import '../../providers/auth/auth_bloc.dart';
import '../../providers/auth/auth_event.dart';
import '../../providers/auth/auth_state.dart';
import 'register_screen.dart';
import 'forgot_password_screen.dart';

/// Login screen for user authentication
class LoginScreen extends StatefulWidget {
  static const String routeName = '/login';

  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: AppColors.surface,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            setState(() {
              _isLoading = true;
            });
          } else {
            setState(() {
              _isLoading = false;
            });
          }

          if (state is AuthAuthenticated) {
            // Navigate to home screen
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const PlaceholderHomeScreen(),
              ),
            );
          } else if (state is AuthError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 40),

                  // App Logo
                  _buildLogo(),

                  const SizedBox(height: 40),

                  // Title and Subtitle
                  _buildHeader(l10n),

                  const SizedBox(height: 32),

                  // Email Field
                  EmailTextField(
                    controller: _emailController,
                    enabled: !_isLoading,
                    autofocus: true,
                    onSubmitted: (_) => _focusPassword(),
                  ),

                  const SizedBox(height: 16),

                  // Password Field
                  PasswordTextField(
                    controller: _passwordController,
                    enabled: !_isLoading,
                    onSubmitted: (_) => _signIn(),
                  ),

                  const SizedBox(height: 8),

                  // Forgot Password Link
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: _isLoading ? null : _navigateToForgotPassword,
                      child: Text(
                        l10n.forgotPassword,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Sign In Button
                  AuthButton(
                    text: l10n.signIn,
                    onPressed: _isLoading ? null : _signIn,
                    isLoading: _isLoading,
                  ),

                  const SizedBox(height: 24),

                  // Divider
                  _buildDivider(l10n),

                  const SizedBox(height: 24),

                  // Social Login Buttons
                  _buildSocialLoginButtons(l10n),

                  const SizedBox(height: 32),

                  // Sign Up Link
                  AuthLinkButton(
                    text: l10n.dontHaveAccount,
                    linkText: l10n.signUp,
                    onPressed: _navigateToSignUp,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Center(
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: const Icon(
          Icons.calendar_today_rounded,
          size: 40,
          color: AppColors.onPrimary,
        ),
      ),
    );
  }

  Widget _buildHeader(AppLocalizations l10n) {
    return Column(
      children: [
        Text(
          l10n.signInTitle,
          style: AppTextStyles.headlineLarge.copyWith(
            color: AppColors.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          l10n.signInSubtitle,
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.onSurface.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDivider(AppLocalizations l10n) {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: AppColors.outline.withOpacity(0.3),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            l10n.orContinueWith,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurface.withOpacity(0.6),
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: AppColors.outline.withOpacity(0.3),
            thickness: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildSocialLoginButtons(AppLocalizations l10n) {
    return Column(
      children: [
        // Google Sign In
        SocialLoginButton(
          text: l10n.signInWithGoogle,
          icon: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(Icons.g_mobiledata, color: Colors.red, size: 20),
          ),
          onPressed: _isLoading ? null : () => _signInWithProvider('google'),
          isLoading: false,
        ),

        const SizedBox(height: 12),

        // Apple Sign In (iOS only)
        if (Theme.of(context).platform == TargetPlatform.iOS)
          SocialLoginButton(
            text: l10n.signInWithApple,
            icon: const Icon(Icons.apple, color: Colors.white, size: 24),
            onPressed: _isLoading ? null : () => _signInWithProvider('apple'),
            isLoading: false,
            backgroundColor: AppColors.onSurface,
            textColor: AppColors.surface,
          ),
      ],
    );
  }

  void _focusPassword() {
    FocusScope.of(context).nextFocus();
  }

  void _signIn() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<AuthBloc>().add(
        AuthSignInRequested(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );
    }
  }

  void _signInWithProvider(String provider) {
    context.read<AuthBloc>().add(AuthOAuthSignInRequested(provider: provider));
  }

  void _navigateToSignUp() {
    Navigator.of(context).pushNamed(RegisterScreen.routeName);
  }

  void _navigateToForgotPassword() {
    Navigator.of(context).pushNamed(ForgotPasswordScreen.routeName);
  }
}

/// Placeholder home screen for development
class PlaceholderHomeScreen extends StatelessWidget {
  const PlaceholderHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              context.read<AuthBloc>().add(const AuthSignOutRequested());
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.construction, size: 64, color: AppColors.primary),
            const SizedBox(height: 16),
            Text(l10n.welcomeToSijilli, style: AppTextStyles.headlineMedium),
            const SizedBox(height: 8),
            Text(l10n.appUnderDevelopment, style: AppTextStyles.bodyMedium),
          ],
        ),
      ),
    );
  }
}
