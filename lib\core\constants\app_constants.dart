/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'سجلي'; // Sijilli in Arabic
  static const String appNameEn = '<PERSON>jilli';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق إدارة المواعيد والمقالات';

  // API Endpoints
  static const String apiVersion = 'v1';

  // Database Tables
  static const String usersTable = 'users';
  static const String appointmentsTable = 'appointments';
  static const String articlesTable = 'articles';
  static const String notificationsTable = 'notifications';
  static const String followsTable = 'follows';
  static const String organizationsTable = 'organizations';

  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userIdKey = 'user_id';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String hijriCorrectionKey = 'hijri_correction';

  // Supported Languages
  static const List<String> supportedLanguages = ['ar', 'en'];
  static const String defaultLanguage = 'ar';

  // Validation
  static const int minPasswordLength = 8;
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 500;
  static const int maxTitleLength = 100;
  static const int maxDescriptionLength = 1000;

  // File Upload
  static const int maxFileSizeBytes = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Time
  static const int sessionTimeoutMinutes = 30;
  static const int notificationRefreshSeconds = 30;
  static const int cacheExpiryHours = 24;

  // UI
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String validationErrorMessage =
      'يرجى التحقق من البيانات المدخلة';
}
