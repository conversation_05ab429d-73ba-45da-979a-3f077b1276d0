/// Utility class for input validation
class Validators {
  // Email regex pattern
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // Username regex pattern (alphanumeric, underscore, hyphen)
  static final RegExp _usernameRegex = RegExp(
    r'^[a-zA-Z0-9_-]+$',
  );

  // Arabic text regex pattern
  static final RegExp _arabicRegex = RegExp(
    r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]+$',
  );

  // Phone number regex pattern (Saudi Arabia format)
  static final RegExp _phoneRegex = RegExp(
    r'^(\+966|966|0)?[5][0-9]{8}$',
  );

  /// Validate email address
  static String? validateEmail(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
    }

    if (!_emailRegex.hasMatch(value)) {
      return locale == 'ar' 
          ? 'يرجى إدخال بريد إلكتروني صحيح' 
          : 'Please enter a valid email';
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required';
    }

    if (value.length < 8) {
      return locale == 'ar' 
          ? 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' 
          : 'Password must be at least 8 characters';
    }

    if (!value.contains(RegExp(r'[A-Z]'))) {
      return locale == 'ar' 
          ? 'كلمة المرور يجب أن تحتوي على حرف كبير' 
          : 'Password must contain an uppercase letter';
    }

    if (!value.contains(RegExp(r'[a-z]'))) {
      return locale == 'ar' 
          ? 'كلمة المرور يجب أن تحتوي على حرف صغير' 
          : 'Password must contain a lowercase letter';
    }

    if (!value.contains(RegExp(r'[0-9]'))) {
      return locale == 'ar' 
          ? 'كلمة المرور يجب أن تحتوي على رقم' 
          : 'Password must contain a number';
    }

    return null;
  }

  /// Validate username
  static String? validateUsername(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'اسم المستخدم مطلوب' : 'Username is required';
    }

    if (value.length < 3) {
      return locale == 'ar' 
          ? 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل' 
          : 'Username must be at least 3 characters';
    }

    if (value.length > 30) {
      return locale == 'ar' 
          ? 'اسم المستخدم يجب أن يكون أقل من 30 حرف' 
          : 'Username must be less than 30 characters';
    }

    if (!_usernameRegex.hasMatch(value)) {
      return locale == 'ar' 
          ? 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط' 
          : 'Username can only contain letters, numbers, underscore and hyphen';
    }

    return null;
  }

  /// Validate full name
  static String? validateFullName(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'الاسم الكامل مطلوب' : 'Full name is required';
    }

    if (value.length < 2) {
      return locale == 'ar' 
          ? 'الاسم يجب أن يكون حرفين على الأقل' 
          : 'Name must be at least 2 characters';
    }

    if (value.length > 50) {
      return locale == 'ar' 
          ? 'الاسم يجب أن يكون أقل من 50 حرف' 
          : 'Name must be less than 50 characters';
    }

    return null;
  }

  /// Validate phone number
  static String? validatePhone(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'رقم الهاتف مطلوب' : 'Phone number is required';
    }

    if (!_phoneRegex.hasMatch(value)) {
      return locale == 'ar' 
          ? 'يرجى إدخال رقم هاتف صحيح' 
          : 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, String fieldName, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? '$fieldName مطلوب' : '$fieldName is required';
    }
    return null;
  }

  /// Validate minimum length
  static String? validateMinLength(String? value, int minLength, String fieldName, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? '$fieldName مطلوب' : '$fieldName is required';
    }

    if (value.length < minLength) {
      return locale == 'ar' 
          ? '$fieldName يجب أن يكون $minLength أحرف على الأقل' 
          : '$fieldName must be at least $minLength characters';
    }

    return null;
  }

  /// Validate maximum length
  static String? validateMaxLength(String? value, int maxLength, String fieldName, {String locale = 'ar'}) {
    if (value != null && value.length > maxLength) {
      return locale == 'ar' 
          ? '$fieldName يجب أن يكون أقل من $maxLength حرف' 
          : '$fieldName must be less than $maxLength characters';
    }

    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return null; // URL is optional
    }

    try {
      final uri = Uri.parse(value);
      if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
        return locale == 'ar' 
            ? 'يرجى إدخال رابط صحيح' 
            : 'Please enter a valid URL';
      }
    } catch (e) {
      return locale == 'ar' 
          ? 'يرجى إدخال رابط صحيح' 
          : 'Please enter a valid URL';
    }

    return null;
  }

  /// Validate Arabic text
  static String? validateArabicText(String? value, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }

    if (!_arabicRegex.hasMatch(value)) {
      return locale == 'ar' 
          ? 'يرجى إدخال نص باللغة العربية فقط' 
          : 'Please enter Arabic text only';
    }

    return null;
  }

  /// Validate confirm password
  static String? validateConfirmPassword(String? value, String? password, {String locale = 'ar'}) {
    if (value == null || value.isEmpty) {
      return locale == 'ar' ? 'تأكيد كلمة المرور مطلوب' : 'Confirm password is required';
    }

    if (value != password) {
      return locale == 'ar' 
          ? 'كلمة المرور غير متطابقة' 
          : 'Passwords do not match';
    }

    return null;
  }
}
