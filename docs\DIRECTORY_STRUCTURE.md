# Sijilli App - Directory Structure

## Complete Project Structure

```
sijilli_app/
├── android/                        # Android platform files
├── ios/                            # iOS platform files
├── linux/                          # Linux platform files
├── macos/                          # macOS platform files
├── web/                            # Web platform files
├── windows/                        # Windows platform files
├── docs/                           # Documentation
│   ├── prd.rtf                     # Product Requirements Document
│   ├── sql_20250530_ohbsrijb4.sql # Database schema
│   ├── PROJECT_ARCHITECTURE.md    # Architecture documentation
│   └── DIRECTORY_STRUCTURE.md     # This file
├── test/                           # Test files
│   └── widget_test.dart            # Widget tests
├── lib/                            # Main source code
│   ├── core/                       # Core application layer
│   │   ├── app/
│   │   │   └── sijilli_app.dart    # Main app widget
│   │   ├── config/
│   │   │   └── app_config.dart     # App configuration
│   │   ├── constants/
│   │   │   └── app_constants.dart  # Application constants
│   │   ├── error/
│   │   │   └── failures.dart       # Error handling
│   │   └── theme/
│   │       ├── app_theme.dart      # Theme configuration
│   │       ├── app_colors.dart     # Color palette
│   │       └── app_text_styles.dart # Typography
│   │
│   ├── data/                       # Data layer
│   │   ├── datasources/            # Data sources
│   │   │   ├── local/              # Local data sources
│   │   │   │   ├── user_local_datasource.dart
│   │   │   │   ├── appointment_local_datasource.dart
│   │   │   │   └── cache_manager.dart
│   │   │   └── remote/             # Remote data sources
│   │   │       ├── user_remote_datasource.dart
│   │   │       ├── appointment_remote_datasource.dart
│   │   │       ├── article_remote_datasource.dart
│   │   │       └── api_client.dart
│   │   ├── models/                 # Data models
│   │   │   ├── user_model.dart     # User data model
│   │   │   ├── appointment_model.dart # Appointment data model
│   │   │   ├── article_model.dart  # Article data model
│   │   │   ├── notification_model.dart # Notification data model
│   │   │   └── organization_model.dart # Organization data model
│   │   └── repositories/           # Repository implementations
│   │       ├── user_repository_impl.dart
│   │       ├── appointment_repository_impl.dart
│   │       ├── article_repository_impl.dart
│   │       └── notification_repository_impl.dart
│   │
│   ├── domain/                     # Business logic layer
│   │   ├── entities/               # Business entities
│   │   │   ├── user_entity.dart    # User entity
│   │   │   ├── appointment_entity.dart # Appointment entity
│   │   │   ├── article_entity.dart # Article entity
│   │   │   ├── notification_entity.dart # Notification entity
│   │   │   └── organization_entity.dart # Organization entity
│   │   ├── repositories/           # Repository interfaces
│   │   │   ├── user_repository.dart
│   │   │   ├── appointment_repository.dart
│   │   │   ├── article_repository.dart
│   │   │   └── notification_repository.dart
│   │   └── usecases/               # Use cases
│   │       ├── usecase.dart        # Base use case
│   │       ├── auth/               # Authentication use cases
│   │       │   ├── login_user.dart
│   │       │   ├── register_user.dart
│   │       │   └── logout_user.dart
│   │       ├── user/               # User use cases
│   │       │   ├── get_current_user.dart
│   │       │   ├── update_user_profile.dart
│   │       │   ├── follow_user.dart
│   │       │   └── search_users.dart
│   │       ├── appointment/        # Appointment use cases
│   │       │   ├── create_appointment.dart
│   │       │   ├── get_appointments.dart
│   │       │   ├── update_appointment.dart
│   │       │   └── delete_appointment.dart
│   │       └── article/            # Article use cases
│   │           ├── create_article.dart
│   │           ├── get_articles.dart
│   │           ├── update_article.dart
│   │           └── delete_article.dart
│   │
│   ├── presentation/               # UI layer
│   │   ├── screens/                # App screens
│   │   │   ├── splash/
│   │   │   │   └── splash_screen.dart
│   │   │   ├── auth/               # Authentication screens
│   │   │   │   ├── login_screen.dart
│   │   │   │   ├── register_screen.dart
│   │   │   │   └── forgot_password_screen.dart
│   │   │   ├── home/               # Home screens
│   │   │   │   ├── home_screen.dart
│   │   │   │   └── dashboard_screen.dart
│   │   │   ├── profile/            # Profile screens
│   │   │   │   ├── profile_screen.dart
│   │   │   │   ├── edit_profile_screen.dart
│   │   │   │   └── user_profile_screen.dart
│   │   │   ├── appointments/       # Appointment screens
│   │   │   │   ├── appointments_screen.dart
│   │   │   │   ├── create_appointment_screen.dart
│   │   │   │   ├── edit_appointment_screen.dart
│   │   │   │   └── appointment_details_screen.dart
│   │   │   ├── articles/           # Article screens
│   │   │   │   ├── articles_screen.dart
│   │   │   │   ├── create_article_screen.dart
│   │   │   │   ├── edit_article_screen.dart
│   │   │   │   └── article_details_screen.dart
│   │   │   ├── notifications/      # Notification screens
│   │   │   │   └── notifications_screen.dart
│   │   │   └── settings/           # Settings screens
│   │   │       ├── settings_screen.dart
│   │   │       ├── language_settings_screen.dart
│   │   │       └── theme_settings_screen.dart
│   │   ├── widgets/                # Reusable widgets
│   │   │   ├── common/             # Common widgets
│   │   │   │   ├── loading_widget.dart
│   │   │   │   ├── error_widget.dart
│   │   │   │   └── empty_state_widget.dart
│   │   │   ├── cards/              # Card widgets
│   │   │   │   ├── appointment_card.dart
│   │   │   │   ├── article_card.dart
│   │   │   │   └── user_card.dart
│   │   │   └── forms/              # Form widgets
│   │   │       ├── appointment_form.dart
│   │   │       ├── article_form.dart
│   │   │       └── user_form.dart
│   │   └── providers/              # State management
│   │       ├── auth/               # Authentication state
│   │       │   ├── auth_bloc.dart
│   │       │   ├── auth_event.dart
│   │       │   └── auth_state.dart
│   │       ├── user/               # User state
│   │       │   ├── user_bloc.dart
│   │       │   ├── user_event.dart
│   │       │   └── user_state.dart
│   │       ├── appointment/        # Appointment state
│   │       │   ├── appointment_bloc.dart
│   │       │   ├── appointment_event.dart
│   │       │   └── appointment_state.dart
│   │       └── article/            # Article state
│   │           ├── article_bloc.dart
│   │           ├── article_event.dart
│   │           └── article_state.dart
│   │
│   ├── shared/                     # Shared utilities and components
│   │   ├── utils/                  # Utility functions
│   │   │   ├── date_utils.dart     # Date/time utilities
│   │   │   ├── validators.dart     # Input validation
│   │   │   ├── formatters.dart     # Text formatters
│   │   │   ├── helpers.dart        # Helper functions
│   │   │   └── extensions.dart     # Dart extensions
│   │   ├── widgets/                # Shared widgets
│   │   │   ├── custom_button.dart  # Custom button widget
│   │   │   ├── custom_text_field.dart # Custom text field widget
│   │   │   ├── custom_app_bar.dart # Custom app bar widget
│   │   │   ├── custom_bottom_nav.dart # Custom bottom navigation
│   │   │   └── custom_dialog.dart  # Custom dialog widget
│   │   ├── services/               # Shared services
│   │   │   ├── navigation_service.dart
│   │   │   ├── notification_service.dart
│   │   │   ├── storage_service.dart
│   │   │   └── analytics_service.dart
│   │   └── constants/              # Shared constants
│   │       ├── api_constants.dart  # API endpoints
│   │       ├── storage_keys.dart   # Storage keys
│   │       └── route_names.dart    # Route names
│   │
│   └── main.dart                   # App entry point
│
├── pubspec.yaml                    # Dependencies and configuration
├── pubspec.lock                    # Dependency lock file
├── analysis_options.yaml          # Dart analysis options
├── README.md                       # Project README
└── sijilli_app.iml               # IntelliJ project file
```

## Layer Descriptions

### Core Layer (`lib/core/`)
Contains application-wide configuration, constants, themes, and error handling.

### Data Layer (`lib/data/`)
Handles data operations including API calls, local storage, and data transformation.

### Domain Layer (`lib/domain/`)
Contains business logic, entities, and use cases. This layer is independent of external frameworks.

### Presentation Layer (`lib/presentation/`)
Contains UI components, screens, widgets, and state management.

### Shared Layer (`lib/shared/`)
Contains utilities, services, and components that are used across multiple layers.

## File Naming Conventions

- **Screens**: `*_screen.dart`
- **Widgets**: `*_widget.dart` or descriptive names
- **Models**: `*_model.dart`
- **Entities**: `*_entity.dart`
- **Repositories**: `*_repository.dart`
- **Use Cases**: Descriptive names like `get_current_user.dart`
- **Blocs**: `*_bloc.dart`, `*_event.dart`, `*_state.dart`

## Directory Guidelines

1. **Group by Feature**: Related files are grouped in feature-specific directories
2. **Layer Separation**: Clear separation between data, domain, and presentation layers
3. **Shared Resources**: Common utilities and widgets are in the shared directory
4. **Scalability**: Structure supports easy addition of new features
5. **Testability**: Each layer can be tested independently
