import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/error/exceptions.dart' as app_exceptions;
import '../models/auth_model.dart';
import '../models/user_model.dart';

/// Remote data source for authentication operations
abstract class AuthRemoteDataSource {
  /// Sign in with email and password
  Future<AuthModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  });

  /// Sign up with email and password
  Future<AuthModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String username,
  });

  /// Sign in with magic link
  Future<void> signInWithMagicLink({required String email, String? redirectTo});

  /// Verify OTP
  Future<AuthModel> verifyOTP({
    required String email,
    required String token,
    required String type,
  });

  /// Reset password
  Future<void> resetPassword({required String email, String? redirectTo});

  /// Update password
  Future<void> updatePassword({
    required String newPassword,
    required String accessToken,
  });

  /// Sign out
  Future<void> signOut();

  /// Get current session
  Future<AuthModel?> getCurrentSession();

  /// Refresh session
  Future<AuthModel> refreshSession({required String refreshToken});

  /// Get current user
  Future<UserModel?> getCurrentUser();

  /// Check if user is authenticated
  Future<bool> isAuthenticated();

  /// Listen to authentication state changes
  Stream<AuthModel> get authStateChanges;

  /// Sign in with OAuth provider
  Future<AuthModel> signInWithOAuth({
    required String provider,
    String? redirectTo,
  });

  /// Delete user account
  Future<void> deleteAccount();

  /// Resend email confirmation
  Future<void> resendEmailConfirmation({required String email});

  /// Update user metadata
  Future<UserModel> updateUserMetadata({
    required Map<String, dynamic> metadata,
  });
}

/// Implementation of AuthRemoteDataSource using Supabase
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final SupabaseClient supabaseClient;

  AuthRemoteDataSourceImpl({required this.supabaseClient});

  @override
  Future<AuthModel> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.session == null) {
        throw const app_exceptions.ServerException(
          'Failed to sign in: No session returned',
        );
      }

      return AuthModel.fromSession(response.session!);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during sign in: $e',
      );
    }
  }

  @override
  Future<AuthModel> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String username,
  }) async {
    try {
      final response = await supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'username': username},
      );

      if (response.session == null) {
        // User needs to confirm email
        if (response.user != null) {
          return AuthModel.fromUser(response.user!);
        }
        throw const app_exceptions.ServerException(
          'Failed to sign up: No user returned',
        );
      }

      return AuthModel.fromSession(response.session!);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during sign up: $e',
      );
    }
  }

  @override
  Future<void> signInWithMagicLink({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await supabaseClient.auth.signInWithOtp(
        email: email,
        emailRedirectTo: redirectTo,
      );
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during magic link sign in: $e',
      );
    }
  }

  @override
  Future<AuthModel> verifyOTP({
    required String email,
    required String token,
    required String type,
  }) async {
    try {
      final response = await supabaseClient.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.values.firstWhere(
          (e) => e.name == type,
          orElse: () => OtpType.magiclink,
        ),
      );

      if (response.session == null) {
        throw const app_exceptions.ServerException(
          'Failed to verify OTP: No session returned',
        );
      }

      return AuthModel.fromSession(response.session!);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during OTP verification: $e',
      );
    }
  }

  @override
  Future<void> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await supabaseClient.auth.resetPasswordForEmail(
        email,
        redirectTo: redirectTo,
      );
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during password reset: $e',
      );
    }
  }

  @override
  Future<void> updatePassword({
    required String newPassword,
    required String accessToken,
  }) async {
    try {
      await supabaseClient.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during password update: $e',
      );
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await supabaseClient.auth.signOut();
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during sign out: $e',
      );
    }
  }

  @override
  Future<AuthModel?> getCurrentSession() async {
    try {
      final session = supabaseClient.auth.currentSession;
      if (session == null) return null;
      return AuthModel.fromSession(session);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error getting current session: $e',
      );
    }
  }

  @override
  Future<AuthModel> refreshSession({required String refreshToken}) async {
    try {
      final response = await supabaseClient.auth.refreshSession();
      if (response.session == null) {
        throw const app_exceptions.ServerException(
          'Failed to refresh session: No session returned',
        );
      }
      return AuthModel.fromSession(response.session!);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during session refresh: $e',
      );
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) return null;

      // Get additional user data from the profiles table
      final profileData =
          await supabaseClient
              .from('profiles')
              .select()
              .eq('user_id', user.id)
              .maybeSingle();

      if (profileData != null) {
        return UserModel.fromJson({
          'id': user.id,
          'username': profileData['username'] ?? '',
          'full_name': profileData['display_name'] ?? '',
          'email': user.email,
          'avatar_url': profileData['profile_picture_url'],
          'profession': null, // Will be added from user_settings if needed
          'bio': profileData['bio'],
          'is_organization': profileData['account_type'] == 'organization',
          'organization_verified':
              false, // Will be determined from profile data
          'created_at': profileData['created_at'] ?? user.createdAt,
          'updated_at':
              profileData['updated_at'] ?? user.updatedAt ?? user.createdAt,
        });
      }

      // Fallback to basic user data
      return UserModel.fromSupabaseUser(user);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error getting current user: $e',
      );
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      final session = supabaseClient.auth.currentSession;
      return session != null && !session.isExpired;
    } catch (e) {
      return false;
    }
  }

  @override
  Stream<AuthModel> get authStateChanges {
    return supabaseClient.auth.onAuthStateChange.map((data) {
      final session = data.session;
      if (session != null) {
        return AuthModel.fromSession(session);
      } else {
        return AuthModel.unauthenticated();
      }
    });
  }

  @override
  Future<AuthModel> signInWithOAuth({
    required String provider,
    String? redirectTo,
  }) async {
    try {
      await supabaseClient.auth.signInWithOAuth(
        OAuthProvider.values.firstWhere(
          (p) => p.name.toLowerCase() == provider.toLowerCase(),
          orElse: () => OAuthProvider.google,
        ),
        redirectTo: redirectTo,
      );

      // OAuth sign-in typically redirects, so we might not get a session immediately
      // The session will be available through the auth state change stream
      return AuthModel.unauthenticated();
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during OAuth sign in: $e',
      );
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      // Note: Supabase doesn't have a direct delete account method
      // This would typically be handled by a server-side function
      // For now, we'll sign out the user
      await signOut();
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error during account deletion: $e',
      );
    }
  }

  @override
  Future<void> resendEmailConfirmation({required String email}) async {
    try {
      await supabaseClient.auth.resend(type: OtpType.signup, email: email);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error resending email confirmation: $e',
      );
    }
  }

  @override
  Future<UserModel> updateUserMetadata({
    required Map<String, dynamic> metadata,
  }) async {
    try {
      final response = await supabaseClient.auth.updateUser(
        UserAttributes(data: metadata),
      );

      if (response.user == null) {
        throw const app_exceptions.ServerException(
          'Failed to update user metadata: No user returned',
        );
      }

      return UserModel.fromSupabaseUser(response.user!);
    } on AuthException catch (e) {
      throw app_exceptions.ServerException(e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
        'Unexpected error updating user metadata: $e',
      );
    }
  }
}
