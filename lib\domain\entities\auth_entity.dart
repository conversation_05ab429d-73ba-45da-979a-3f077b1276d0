import 'package:equatable/equatable.dart';

/// Authentication entity representing authentication state and user session
class AuthEntity extends Equatable {
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final String? userId;
  final String? email;
  final bool isAuthenticated;

  const AuthEntity({
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.userId,
    this.email,
    this.isAuthenticated = false,
  });

  /// Create an authenticated auth entity
  const AuthEntity.authenticated({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.userId,
    required this.email,
  }) : isAuthenticated = true;

  /// Create an unauthenticated auth entity
  const AuthEntity.unauthenticated()
      : accessToken = null,
        refreshToken = null,
        expiresAt = null,
        userId = null,
        email = null,
        isAuthenticated = false;

  /// Check if the session is expired
  bool get isExpired {
    if (expiresAt == null) return true;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if the session is valid (authenticated and not expired)
  bool get isValid => isAuthenticated && !isExpired;

  /// Copy with new values
  AuthEntity copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    String? userId,
    String? email,
    bool? isAuthenticated,
  }) {
    return AuthEntity(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        expiresAt,
        userId,
        email,
        isAuthenticated,
      ];
}

/// Login request entity
class LoginRequest extends Equatable {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

/// Registration request entity
class RegisterRequest extends Equatable {
  final String email;
  final String password;
  final String fullName;
  final String username;

  const RegisterRequest({
    required this.email,
    required this.password,
    required this.fullName,
    required this.username,
  });

  @override
  List<Object> get props => [email, password, fullName, username];
}

/// Password reset request entity
class PasswordResetRequest extends Equatable {
  final String email;

  const PasswordResetRequest({
    required this.email,
  });

  @override
  List<Object> get props => [email];
}

/// Password update request entity
class PasswordUpdateRequest extends Equatable {
  final String newPassword;
  final String accessToken;

  const PasswordUpdateRequest({
    required this.newPassword,
    required this.accessToken,
  });

  @override
  List<Object> get props => [newPassword, accessToken];
}
