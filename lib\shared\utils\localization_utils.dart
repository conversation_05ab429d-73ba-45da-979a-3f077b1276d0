import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Utility class for localization helpers
class LocalizationUtils {
  /// Get text direction for locale
  static TextDirection getTextDirection(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
      case 'he':
      case 'fa':
      case 'ur':
        return TextDirection.rtl;
      default:
        return TextDirection.ltr;
    }
  }

  /// Check if locale is RTL
  static bool isRTL(Locale locale) {
    return getTextDirection(locale) == TextDirection.rtl;
  }

  /// Get locale-specific number format
  static String formatNumber(num number, Locale locale) {
    final formatter = NumberFormat.decimalPattern(locale.toString());
    return formatter.format(number);
  }

  /// Get locale-specific currency format
  static String formatCurrency(num amount, Locale locale, {String? symbol}) {
    final formatter = NumberFormat.currency(
      locale: locale.toString(),
      symbol: symbol ?? _getCurrencySymbol(locale),
    );
    return formatter.format(amount);
  }

  /// Get locale-specific date format
  static String formatDate(DateTime date, Locale locale, {String? pattern}) {
    final formatter = DateFormat(pattern ?? 'dd/MM/yyyy', locale.toString());
    return formatter.format(date);
  }

  /// Get locale-specific time format
  static String formatTime(DateTime time, Locale locale, {bool use24Hour = true}) {
    final pattern = use24Hour ? 'HH:mm' : 'hh:mm a';
    final formatter = DateFormat(pattern, locale.toString());
    return formatter.format(time);
  }

  /// Get locale-specific datetime format
  static String formatDateTime(DateTime dateTime, Locale locale) {
    final formatter = DateFormat.yMd(locale.toString()).add_jm();
    return formatter.format(dateTime);
  }

  /// Get relative time string
  static String getRelativeTime(DateTime dateTime, Locale locale) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (locale.languageCode == 'ar') {
      return _getRelativeTimeArabic(difference);
    } else {
      return _getRelativeTimeEnglish(difference);
    }
  }

  /// Get day name for locale
  static String getDayName(DateTime date, Locale locale) {
    final formatter = DateFormat.EEEE(locale.toString());
    return formatter.format(date);
  }

  /// Get month name for locale
  static String getMonthName(DateTime date, Locale locale) {
    final formatter = DateFormat.MMMM(locale.toString());
    return formatter.format(date);
  }

  /// Get short day name for locale
  static String getShortDayName(DateTime date, Locale locale) {
    final formatter = DateFormat.E(locale.toString());
    return formatter.format(date);
  }

  /// Get short month name for locale
  static String getShortMonthName(DateTime date, Locale locale) {
    final formatter = DateFormat.MMM(locale.toString());
    return formatter.format(date);
  }

  /// Convert numbers to locale-specific format
  static String localizeNumbers(String text, Locale locale) {
    if (locale.languageCode == 'ar') {
      return _convertToArabicNumbers(text);
    }
    return text;
  }

  /// Get currency symbol for locale
  static String _getCurrencySymbol(Locale locale) {
    switch (locale.countryCode) {
      case 'SA':
        return 'ر.س';
      case 'US':
        return '\$';
      case 'GB':
        return '£';
      case 'EU':
        return '€';
      default:
        return '';
    }
  }

  /// Get relative time in Arabic
  static String _getRelativeTimeArabic(Duration difference) {
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      if (minutes == 1) return 'منذ دقيقة';
      if (minutes == 2) return 'منذ دقيقتين';
      if (minutes <= 10) return 'منذ $minutes دقائق';
      return 'منذ $minutes دقيقة';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      if (hours == 1) return 'منذ ساعة';
      if (hours == 2) return 'منذ ساعتين';
      if (hours <= 10) return 'منذ $hours ساعات';
      return 'منذ $hours ساعة';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      if (days == 1) return 'منذ يوم';
      if (days == 2) return 'منذ يومين';
      if (days <= 10) return 'منذ $days أيام';
      return 'منذ $days يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      if (weeks == 1) return 'منذ أسبوع';
      if (weeks == 2) return 'منذ أسبوعين';
      if (weeks <= 10) return 'منذ $weeks أسابيع';
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      if (months == 1) return 'منذ شهر';
      if (months == 2) return 'منذ شهرين';
      if (months <= 10) return 'منذ $months أشهر';
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      if (years == 1) return 'منذ سنة';
      if (years == 2) return 'منذ سنتين';
      if (years <= 10) return 'منذ $years سنوات';
      return 'منذ $years سنة';
    }
  }

  /// Get relative time in English
  static String _getRelativeTimeEnglish(Duration difference) {
    if (difference.inMinutes < 1) {
      return 'Now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return minutes == 1 ? '1 minute ago' : '$minutes minutes ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return hours == 1 ? '1 hour ago' : '$hours hours ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return days == 1 ? '1 day ago' : '$days days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    }
  }

  /// Convert English numbers to Arabic numbers
  static String _convertToArabicNumbers(String text) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = text;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }

  /// Convert Arabic numbers to English numbers
  static String convertToEnglishNumbers(String text) {
    const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = text;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }

  /// Get plural form for Arabic
  static String getArabicPlural(int count, String singular, String dual, String plural) {
    if (count == 0) return plural;
    if (count == 1) return singular;
    if (count == 2) return dual;
    if (count >= 3 && count <= 10) return plural;
    return singular; // For 11+ in Arabic
  }

  /// Get plural form for English
  static String getEnglishPlural(int count, String singular, String plural) {
    return count == 1 ? singular : plural;
  }

  /// Format file size with locale
  static String formatFileSize(int bytes, Locale locale) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const arabicSuffixes = ['بايت', 'ك.بايت', 'م.بايت', 'ج.بايت', 'ت.بايت'];
    
    if (bytes == 0) return '0 ${locale.languageCode == 'ar' ? arabicSuffixes[0] : suffixes[0]}';
    
    final i = (bytes.bitLength - 1) ~/ 10;
    final size = bytes / (1 << (i * 10));
    final suffix = locale.languageCode == 'ar' ? arabicSuffixes[i] : suffixes[i];
    
    return '${formatNumber(size, locale)} $suffix';
  }
}
