# Supabase Setup Guide for Sijilli Authentication

## Overview

This guide walks you through setting up Supabase for the Sijilli app authentication system.

## Prerequisites

1. Supabase account (sign up at https://supabase.com)
2. Basic understanding of SQL and database concepts

## Step 1: Create Supabase Project

1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `sijilli-app`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
5. Click "Create new project"
6. Wait for project initialization (2-3 minutes)

## Step 2: Configure Authentication

### Basic Auth Settings

1. Go to **Authentication > Settings**
2. Configure the following:

```
Site URL: http://localhost:3000 (for development)
Additional Redirect URLs: 
- http://localhost:3000/auth/callback
- https://yourdomain.com/auth/callback (for production)

JWT Expiry: 3600 (1 hour)
Refresh Token Expiry: 604800 (7 days)
```

### Email Settings

1. In **Authentication > Settings > Email**
2. Configure email templates:

**Confirm Signup Template:**
```html
<h2>Welcome to Sijilli!</h2>
<p>Please confirm your email address by clicking the link below:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm Email</a></p>
<p>If you didn't create an account, you can safely ignore this email.</p>
```

**Reset Password Template:**
```html
<h2>Reset Your Password</h2>
<p>Click the link below to reset your password:</p>
<p><a href="{{ .ConfirmationURL }}">Reset Password</a></p>
<p>If you didn't request this, you can safely ignore this email.</p>
```

### Social Providers (Optional)

#### Google OAuth Setup

1. Go to **Authentication > Providers**
2. Enable Google provider
3. Add your Google OAuth credentials:
   - **Client ID**: From Google Cloud Console
   - **Client Secret**: From Google Cloud Console

#### Apple OAuth Setup (iOS)

1. Enable Apple provider
2. Add your Apple OAuth credentials:
   - **Client ID**: From Apple Developer Console
   - **Client Secret**: From Apple Developer Console

## Step 3: Database Schema

### Users Table Enhancement

The default `auth.users` table is sufficient, but you may want to create a public `users` table for additional profile information:

```sql
-- Create users table for additional profile data
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    profession TEXT,
    bio TEXT,
    is_organization BOOLEAN DEFAULT FALSE,
    organization_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index on username
CREATE UNIQUE INDEX users_username_idx ON public.users(username);

-- Enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, username, full_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'User')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
```

### Username Validation Function

```sql
-- Create function to validate username
CREATE OR REPLACE FUNCTION public.is_username_available(username_to_check TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT EXISTS (
        SELECT 1 FROM public.users WHERE username = username_to_check
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Step 4: Row Level Security (RLS) Policies

### Additional Security Policies

```sql
-- Policy for public profile viewing (optional)
CREATE POLICY "Public profiles are viewable by everyone" ON public.users
    FOR SELECT USING (true);

-- Policy to prevent username changes after creation
CREATE POLICY "Username cannot be changed" ON public.users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (username = OLD.username OR OLD.username IS NULL);
```

## Step 5: API Keys and Configuration

### Get Your API Keys

1. Go to **Settings > API**
2. Copy the following:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (Keep this secret!)

### Update App Configuration

Update `lib/core/config/app_config.dart`:

```dart
class AppConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'https://your-project-id.supabase.co';
  static const String supabaseAnonKey = 'your-anon-key-here';
  
  // Never expose the service key in client code!
  // Use it only in server-side functions or admin operations
}
```

## Step 6: Environment Variables

For production, use environment variables:

### Flutter Web
Create `.env` file:
```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
```

### Flutter Mobile
Use `flutter_dotenv` package and create environment-specific configs.

## Step 7: Testing the Setup

### Test Authentication

1. **Sign Up Test:**
   ```sql
   -- Check if user was created
   SELECT * FROM auth.users WHERE email = '<EMAIL>';
   SELECT * FROM public.users WHERE id = 'user-id-here';
   ```

2. **Sign In Test:**
   - Use Supabase dashboard Auth section
   - Check user sessions

3. **Password Reset Test:**
   - Trigger password reset
   - Check email delivery
   - Verify reset link works

## Step 8: Production Considerations

### Security Checklist

- ✅ RLS policies are enabled and tested
- ✅ Service key is not exposed in client code
- ✅ CORS settings are configured properly
- ✅ Rate limiting is enabled
- ✅ Email templates are customized
- ✅ Redirect URLs are whitelisted

### Performance Optimization

1. **Database Indexes:**
   ```sql
   -- Add indexes for common queries
   CREATE INDEX users_username_lower_idx ON public.users(LOWER(username));
   CREATE INDEX users_created_at_idx ON public.users(created_at);
   ```

2. **Connection Pooling:**
   - Configure appropriate connection limits
   - Monitor database performance

### Monitoring and Logging

1. Enable **Database > Logs** for monitoring
2. Set up **Auth > Logs** for authentication events
3. Configure alerts for suspicious activities

## Step 9: Backup and Recovery

### Automated Backups

1. Go to **Settings > Database**
2. Enable automated backups
3. Configure backup retention period
4. Test backup restoration process

### Manual Backup

```bash
# Export schema and data
pg_dump -h db.your-project-id.supabase.co -U postgres -d postgres > backup.sql
```

## Troubleshooting

### Common Issues

1. **Email not sending:**
   - Check SMTP configuration
   - Verify email templates
   - Check spam folder

2. **RLS blocking queries:**
   - Verify policies are correct
   - Check user authentication state
   - Test policies in SQL editor

3. **CORS errors:**
   - Add your domain to allowed origins
   - Check redirect URLs configuration

4. **JWT token issues:**
   - Verify token expiry settings
   - Check token validation logic
   - Ensure proper token storage

### Debug Queries

```sql
-- Check user authentication
SELECT auth.uid(), auth.jwt();

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'users';

-- Check user sessions
SELECT * FROM auth.sessions WHERE user_id = auth.uid();
```

## Support and Resources

- **Supabase Documentation**: https://supabase.com/docs
- **Authentication Guide**: https://supabase.com/docs/guides/auth
- **RLS Guide**: https://supabase.com/docs/guides/auth/row-level-security
- **Community Discord**: https://discord.supabase.com
