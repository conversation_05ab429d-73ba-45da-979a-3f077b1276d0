-- 1. جدول المستخدمين
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    avatar_url TEXT,
    profession TEXT,
    bio TEXT,
    is_organization BOOLEAN DEFAULT FALSE,
    organization_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. جدول المواعيد
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    link TEXT,
    region TEXT NOT NULL,
    building TEXT NOT NULL,
    date_gregorian DATE NOT NULL,
    date_hijri DATE,
    time_start TIME,
    time_end TIME,
    all_day BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    sun_set_time TIME,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 3. جدول المشاركين في الموعد
CREATE TYPE participant_status AS ENUM ('invited', 'accepted', 'declined', 'deleted');

CREATE TABLE appointment_participants (
    appointment_id UUID REFERENCES appointments(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status participant_status DEFAULT 'invited',
    invited_at TIMESTAMP DEFAULT NOW(),
    responded_at TIMESTAMP,
    deleted_at TIMESTAMP,
    is_owner BOOLEAN DEFAULT FALSE,
    PRIMARY KEY (appointment_id, user_id)
);

-- 4. جدول المقالات
CREATE TABLE articles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 5. جدول المشاركين في المقالات
CREATE TABLE article_participants (
    article_id UUID REFERENCES articles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'viewer',
    invited_at TIMESTAMP DEFAULT NOW(),
    accepted_at TIMESTAMP,
    PRIMARY KEY (article_id, user_id)
);

-- 6. جدول الإشعارات
CREATE TYPE notification_type AS ENUM (
    'invitation', 'accept', 'decline', 'delete', 
    'reminder', 'return_request', 'edit_request'
);

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type notification_type,
    related_type TEXT,
    related_id UUID,
    message TEXT,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 7. جدول الزيارات
CREATE TABLE visits (
    visitor_id UUID REFERENCES users(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE CASCADE,
    visited_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (visitor_id, appointment_id)
);

-- 8. جدول المحذوفات المؤقتة
CREATE TABLE trash (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    item_type TEXT,
    item_id UUID,
    deleted_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP DEFAULT NOW() + INTERVAL '30 days'
);

-- 9. جدول بيانات المؤسسات
CREATE TABLE organizations (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    verified BOOLEAN DEFAULT FALSE,
    banner_url TEXT,
    fixed_region TEXT,
    fixed_building TEXT,
    social_links JSONB
);

-- 10. جدول إعادة النشر
CREATE TABLE reposts (
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    appointment_id UUID REFERENCES appointments(id) ON DELETE CASCADE,
    original_owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reposted_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (user_id, appointment_id)
);

-- 11. جدول المتابعة بين المستخدمين
CREATE TYPE relationship_type AS ENUM ('following', 'followed_back', 'friend');

CREATE TABLE follows (
    follower_id UUID REFERENCES users(id) ON DELETE CASCADE,
    following_id UUID REFERENCES users(id) ON DELETE CASCADE,
    relationship relationship_type DEFAULT 'following',
    followed_at TIMESTAMP DEFAULT NOW(),
    PRIMARY KEY (follower_id, following_id)
);

-- 12. جدول إعدادات المستخدم
CREATE TABLE user_settings (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    theme TEXT DEFAULT 'light',
    language TEXT DEFAULT 'ar',
    number_format TEXT DEFAULT 'arabic',
    font_family TEXT DEFAULT 'sans-serif',
    hijri_correction INTEGER DEFAULT 0
);

-- ███████████████████████████████████████
-- 🔐 تفعيل Row Level Security (RLS)
-- ███████████████████████████████████████

-- تفعيل RLS على جميع الجداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointment_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE trash ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE visits ENABLE ROW LEVEL SECURITY;
ALTER TABLE reposts ENABLE ROW LEVEL SECURITY;

-- ███████████████████████████████████████
-- 🛡️ سياسات الأمان (Row Level Security Policies)
-- ███████████████████████████████████████

-- 1. جدول المستخدمين: السماح للمجهولين برؤية مستخدم واحد فقط عبر الرابط
CREATE POLICY select_user_by_id ON users FOR SELECT TO anon
USING (true); -- سيتم تعديلها لاحقًا لتسمح فقط بالحسابات العامة

-- السماح للمسجلين برؤية جميع المستخدمين
CREATE POLICY select_users_authenticated ON users FOR SELECT TO authenticated
USING (true);

-- 2. جدول المواعيد: السماح للمجهولين برؤية المواعيد العامة فقط
CREATE POLICY select_public_appointments_for_anon ON appointments FOR SELECT TO anon
USING (is_public = true);

-- السماح للمستخدمين المسجلين برؤية مواعيدهم فقط (مالك أو مدعو)
CREATE POLICY select_own_appointments ON appointments FOR SELECT TO authenticated
USING (
    owner_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM appointment_participants
        WHERE appointment_id = appointments.id AND user_id = auth.uid()
    )
);

-- 3. جدول المشاركين في الموعد: السماح للمستخدمين برؤية المشاركين في المواعيد التي يخصونها
CREATE POLICY select_participants_authenticated ON appointment_participants FOR SELECT TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM appointments
        WHERE id = appointment_id AND (
            owner_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM appointment_participants
                WHERE appointment_id = appointments.id AND user_id = auth.uid()
            )
        )
    )
);

-- 4. جدول الإشعارات: السماح للمستخدم برؤية إشعاراته فقط
CREATE POLICY select_own_notifications ON notifications FOR SELECT TO authenticated
USING (user_id = auth.uid());

-- 5. جدول المقالات: السماح للمجهولين برؤية المقالات العامة فقط
CREATE POLICY select_public_articles_for_anon ON articles FOR SELECT TO anon
USING (is_public = true);

-- السماح للمستخدمين برؤية مقالاتهم فقط
CREATE POLICY select_own_articles ON articles FOR SELECT TO authenticated
USING (
    owner_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM article_participants
        WHERE article_id = articles.id AND user_id = auth.uid()
    )
);

-- 6. جدول إعادة النشر: السماح للمستخدمين بإعادة نشر مواعيد المؤسسات العامة فقط
CREATE POLICY insert_reposts ON reposts FOR INSERT TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM appointments
        WHERE id = appointment_id AND is_public = true
    ) AND (
        SELECT organization_verified FROM users WHERE id = auth.uid()
    ) = true
);