import 'package:equatable/equatable.dart';

/// User entity representing a user in the system
class UserEntity extends Equatable {
  final String id;
  final String username;
  final String fullName;
  final String email;
  final String? avatarUrl;
  final String? profession;
  final String? bio;
  final bool isOrganization;
  final bool organizationVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserEntity({
    required this.id,
    required this.username,
    required this.fullName,
    required this.email,
    this.avatarUrl,
    this.profession,
    this.bio,
    this.isOrganization = false,
    this.organizationVerified = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a copy of this user with updated fields
  UserEntity copyWith({
    String? id,
    String? username,
    String? fullName,
    String? email,
    String? avatarUrl,
    String? profession,
    String? bio,
    bool? isOrganization,
    bool? organizationVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserEntity(
      id: id ?? this.id,
      username: username ?? this.username,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      profession: profession ?? this.profession,
      bio: bio ?? this.bio,
      isOrganization: isOrganization ?? this.isOrganization,
      organizationVerified: organizationVerified ?? this.organizationVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get display name (full name or username)
  String get displayName => fullName.isNotEmpty ? fullName : username;

  /// Check if user has a complete profile
  bool get hasCompleteProfile {
    return fullName.isNotEmpty && 
           email.isNotEmpty && 
           (bio?.isNotEmpty ?? false);
  }

  /// Check if user is verified organization
  bool get isVerifiedOrganization => isOrganization && organizationVerified;

  @override
  List<Object?> get props => [
        id,
        username,
        fullName,
        email,
        avatarUrl,
        profession,
        bio,
        isOrganization,
        organizationVerified,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'UserEntity(id: $id, username: $username, fullName: $fullName, email: $email)';
  }
}
