{"@@locale": "en", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "<PERSON><PERSON><PERSON>", "@appName": {"description": "The name of the application"}, "appDescription": "Appointment and Article Management App", "@appDescription": {"description": "Brief description of the app"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "welcomeToSijilli": "Welcome to <PERSON><PERSON>lli", "@welcomeToSijilli": {"description": "Welcome message with app name"}, "appUnderDevelopment": "App under development", "@appUnderDevelopment": {"description": "Message shown when app is in development"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Generic error message"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "save": "Save", "@save": {"description": "Save button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "create": "Create", "@create": {"description": "Create button text"}, "update": "Update", "@update": {"description": "Update button text"}, "confirm": "Confirm", "@confirm": {"description": "Confirm button text"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "done": "Done", "@done": {"description": "Done button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "back": "Back", "@back": {"description": "Back button text"}, "close": "Close", "@close": {"description": "Close button text"}, "search": "Search", "@search": {"description": "Search button/field text"}, "searchHint": "Search...", "@searchHint": {"description": "Search field hint text"}, "noResults": "No results found", "@noResults": {"description": "Message when search returns no results"}, "emptyList": "No items to display", "@emptyList": {"description": "Message when list is empty"}, "networkError": "Network connection error", "@networkError": {"description": "Network error message"}, "unknownError": "An unexpected error occurred", "@unknownError": {"description": "Unknown error message"}, "validationError": "Please check the entered data", "@validationError": {"description": "Validation error message"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "register": "Register", "@register": {"description": "Register button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "email": "Email", "@email": {"description": "Email field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "username": "Username", "@username": {"description": "Username field label"}, "fullName": "Full Name", "@fullName": {"description": "Full name field label"}, "signIn": "Sign In", "@signIn": {"description": "Sign in button text"}, "signUp": "Sign Up", "@signUp": {"description": "Sign up button text"}, "signOut": "Sign Out", "@signOut": {"description": "Sign out button text"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "resetPassword": "Reset Password", "@resetPassword": {"description": "Reset password screen title"}, "changePassword": "Change Password", "@changePassword": {"description": "Change password screen title"}, "newPassword": "New Password", "@newPassword": {"description": "New password field label"}, "currentPassword": "Current Password", "@currentPassword": {"description": "Current password field label"}, "emailHint": "Enter your email", "@emailHint": {"description": "Email field hint text"}, "passwordHint": "Enter your password", "@passwordHint": {"description": "Password field hint text"}, "confirmPasswordHint": "Re-enter your password", "@confirmPasswordHint": {"description": "Confirm password field hint text"}, "usernameHint": "Enter your username", "@usernameHint": {"description": "Username field hint text"}, "fullNameHint": "Enter your full name", "@fullNameHint": {"description": "Full name field hint text"}, "emailRequired": "Email is required", "@emailRequired": {"description": "Email required validation message"}, "passwordRequired": "Password is required", "@passwordRequired": {"description": "Password required validation message"}, "usernameRequired": "Username is required", "@usernameRequired": {"description": "Username required validation message"}, "fullNameRequired": "Full name is required", "@fullNameRequired": {"description": "Full name required validation message"}, "invalidEmail": "Please enter a valid email", "@invalidEmail": {"description": "Invalid email validation message"}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Password too short validation message"}, "passwordsDoNotMatch": "Passwords do not match", "@passwordsDoNotMatch": {"description": "Passwords mismatch validation message"}, "usernameInvalid": "Username must be 3-24 characters with letters and numbers only", "@usernameInvalid": {"description": "Invalid username validation message"}, "signInTitle": "Welcome Back", "@signInTitle": {"description": "Sign in screen title"}, "signInSubtitle": "Sign in to continue", "@signInSubtitle": {"description": "Sign in screen subtitle"}, "signUpTitle": "Create New Account", "@signUpTitle": {"description": "Sign up screen title"}, "signUpSubtitle": "Join the Sijilli community", "@signUpSubtitle": {"description": "Sign up screen subtitle"}, "forgotPasswordTitle": "Forgot Password", "@forgotPasswordTitle": {"description": "Forgot password screen title"}, "forgotPasswordSubtitle": "Enter your email to reset your password", "@forgotPasswordSubtitle": {"description": "Forgot password screen subtitle"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Link text to sign in screen"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Link text to sign up screen"}, "backToSignIn": "Back to Sign In", "@backToSignIn": {"description": "Link text to go back to sign in"}, "signInWithGoogle": "Sign in with Google", "@signInWithGoogle": {"description": "Google sign in button text"}, "signInWithApple": "Sign in with Apple", "@signInWithApple": {"description": "Apple sign in button text"}, "signInWithFacebook": "Sign in with Facebook", "@signInWithFacebook": {"description": "Facebook sign in button text"}, "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Divider text for social login options"}, "sendResetLink": "Send Reset Link", "@sendResetLink": {"description": "Send password reset link button text"}, "resetLinkSent": "Password reset link sent to your email", "@resetLinkSent": {"description": "Password reset link sent confirmation message"}, "checkYourEmail": "Check your email", "@checkYourEmail": {"description": "Check email instruction"}, "emailConfirmationSent": "Email confirmation link sent to your email", "@emailConfirmationSent": {"description": "Email confirmation sent message"}, "resendConfirmation": "Resend Confirmation", "@resendConfirmation": {"description": "Resend email confirmation button text"}, "signInSuccess": "Signed in successfully", "@signInSuccess": {"description": "Sign in success message"}, "signUpSuccess": "Account created successfully", "@signUpSuccess": {"description": "Sign up success message"}, "signOutSuccess": "Signed out successfully", "@signOutSuccess": {"description": "Sign out success message"}, "passwordResetSuccess": "Password reset link sent", "@passwordResetSuccess": {"description": "Password reset success message"}, "passwordUpdateSuccess": "Password updated successfully", "@passwordUpdateSuccess": {"description": "Password update success message"}, "authError": "Authentication error", "@authError": {"description": "Generic authentication error"}, "invalidCredentials": "Invalid credentials", "@invalidCredentials": {"description": "Invalid login credentials error"}, "userNotFound": "User not found", "@userNotFound": {"description": "User not found error"}, "emailAlreadyExists": "Email already in use", "@emailAlreadyExists": {"description": "Email already exists error"}, "usernameAlreadyExists": "Username already in use", "@usernameAlreadyExists": {"description": "Username already exists error"}, "weakPassword": "Password is too weak", "@weakPassword": {"description": "Weak password error"}, "tooManyRequests": "Too many requests, please try again later", "@tooManyRequests": {"description": "Too many requests error"}, "sessionExpired": "Session expired, please sign in again", "@sessionExpired": {"description": "Session expired error"}, "phone": "Phone Number", "@phone": {"description": "Phone number field label"}, "bio": "Bio", "@bio": {"description": "Bio field label"}, "profession": "Profession", "@profession": {"description": "Profession field label"}, "profile": "Profile", "@profile": {"description": "Profile screen title"}, "editProfile": "Edit Profile", "@editProfile": {"description": "Edit profile screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "language": "Language", "@language": {"description": "Language setting label"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "lightTheme": "Light", "@lightTheme": {"description": "Light theme option"}, "darkTheme": "Dark", "@darkTheme": {"description": "Dark theme option"}, "systemTheme": "System", "@systemTheme": {"description": "System theme option"}, "arabic": "العربية", "@arabic": {"description": "Arabic language option"}, "english": "English", "@english": {"description": "English language option"}, "appointments": "Appointments", "@appointments": {"description": "Appointments screen title"}, "createAppointment": "Create Appointment", "@createAppointment": {"description": "Create appointment screen title"}, "editAppointment": "Edit Appointment", "@editAppointment": {"description": "Edit appointment screen title"}, "appointmentDetails": "Appointment Details", "@appointmentDetails": {"description": "Appointment details screen title"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "startTime": "Start Time", "@startTime": {"description": "Start time field label"}, "endTime": "End Time", "@endTime": {"description": "End time field label"}, "location": "Location", "@location": {"description": "Location field label"}, "region": "Region", "@region": {"description": "Region field label"}, "building": "Building", "@building": {"description": "Building field label"}, "allDay": "All Day", "@allDay": {"description": "All day appointment option"}, "isPublic": "Public", "@isPublic": {"description": "Public appointment option"}, "articles": "Articles", "@articles": {"description": "Articles screen title"}, "createArticle": "Create Article", "@createArticle": {"description": "Create article screen title"}, "editArticle": "Edit Article", "@editArticle": {"description": "Edit article screen title"}, "articleDetails": "Article Details", "@articleDetails": {"description": "Article details screen title"}, "content": "Content", "@content": {"description": "Content field label"}, "notifications": "Notifications", "@notifications": {"description": "Notifications screen title"}, "noNotifications": "No notifications", "@noNotifications": {"description": "Message when there are no notifications"}, "markAsRead": "<PERSON> <PERSON>", "@markAsRead": {"description": "Mark notification as read"}, "markAllAsRead": "<PERSON> as <PERSON>", "@markAllAsRead": {"description": "Mark all notifications as read"}, "followers": "Followers", "@followers": {"description": "Followers count label"}, "following": "Following", "@following": {"description": "Following count label"}, "follow": "Follow", "@follow": {"description": "Follow button text"}, "unfollow": "Unfollow", "@unfollow": {"description": "Unfollow button text"}, "today": "Today", "@today": {"description": "Today date label"}, "tomorrow": "Tomorrow", "@tomorrow": {"description": "Tomorrow date label"}, "yesterday": "Yesterday", "@yesterday": {"description": "Yesterday date label"}, "thisWeek": "This Week", "@thisWeek": {"description": "This week date range"}, "thisMonth": "This Month", "@thisMonth": {"description": "This month date range"}, "hijriDate": "<PERSON><PERSON><PERSON> Date", "@hijriDate": {"description": "Hijri date label"}, "gregorianDate": "Gregorian Date", "@gregorianDate": {"description": "Gregorian date label"}, "hijriCorrection": "Hijri Correction", "@hijriCorrection": {"description": "Hijri date correction setting"}, "organization": "Organization", "@organization": {"description": "Organization label"}, "verified": "Verified", "@verified": {"description": "Verified account badge"}, "repost": "Repost", "@repost": {"description": "Repost button text"}, "share": "Share", "@share": {"description": "Share button text"}, "link": "Link", "@link": {"description": "Link field label"}, "url": "URL", "@url": {"description": "URL field label"}, "website": "Website", "@website": {"description": "Website field label"}, "socialLinks": "Social Links", "@socialLinks": {"description": "Social links section title"}, "avatar": "Avatar", "@avatar": {"description": "Avatar/profile picture label"}, "banner": "Banner", "@banner": {"description": "Banner image label"}, "uploadImage": "Upload Image", "@uploadImage": {"description": "Upload image button text"}, "changeImage": "Change Image", "@changeImage": {"description": "Change image button text"}, "removeImage": "Remove Image", "@removeImage": {"description": "Remove image button text"}, "camera": "Camera", "@camera": {"description": "Camera option for image selection"}, "gallery": "Gallery", "@gallery": {"description": "Gallery option for image selection"}, "selectImageSource": "Select Image Source", "@selectImageSource": {"description": "Dialog title for image source selection"}, "permissionDenied": "Permission denied", "@permissionDenied": {"description": "Permission denied error message"}, "cameraPermissionRequired": "Camera permission is required", "@cameraPermissionRequired": {"description": "Camera permission required message"}, "storagePermissionRequired": "Storage permission is required", "@storagePermissionRequired": {"description": "Storage permission required message"}, "deleteConfirmation": "Are you sure you want to delete this item?", "@deleteConfirmation": {"description": "Delete confirmation dialog message"}, "logoutConfirmation": "Are you sure you want to logout?", "@logoutConfirmation": {"description": "Logout confirmation dialog message"}, "unsavedChanges": "You have unsaved changes. Do you want to discard them?", "@unsavedChanges": {"description": "Unsaved changes warning message"}, "discard": "Discard", "@discard": {"description": "Discard changes button text"}, "keepEditing": "Keep Editing", "@keepEditing": {"description": "Keep editing button text"}}