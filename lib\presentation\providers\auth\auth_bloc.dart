import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/repositories/auth_repository.dart';
import '../../../domain/usecases/auth/login_user.dart';
import '../../../domain/usecases/auth/register_user.dart';
import '../../../domain/usecases/auth/reset_password.dart';
import '../../../domain/usecases/auth/logout_user.dart';
import '../../../domain/usecases/usecase.dart';
import 'auth_event.dart';
import 'auth_state.dart';

/// BLoC for managing authentication state
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final LoginUser _loginUser;
  final RegisterUser _registerUser;
  final ResetPassword _resetPassword;
  final LogoutUser _logoutUser;

  late StreamSubscription _authStateSubscription;

  AuthBloc({
    required AuthRepository authRepository,
    required LoginUser loginUser,
    required RegisterUser registerUser,
    required ResetPassword resetPassword,
    required LogoutUser logoutUser,
  })  : _authRepository = authRepository,
        _loginUser = loginUser,
        _registerUser = registerUser,
        _resetPassword = resetPassword,
        _logoutUser = logoutUser,
        super(const AuthInitial()) {
    // Register event handlers
    on<AuthCheckRequested>(_onAuthCheckRequested);
    on<AuthSignInRequested>(_onAuthSignInRequested);
    on<AuthSignUpRequested>(_onAuthSignUpRequested);
    on<AuthMagicLinkRequested>(_onAuthMagicLinkRequested);
    on<AuthOTPVerificationRequested>(_onAuthOTPVerificationRequested);
    on<AuthPasswordResetRequested>(_onAuthPasswordResetRequested);
    on<AuthPasswordUpdateRequested>(_onAuthPasswordUpdateRequested);
    on<AuthSignOutRequested>(_onAuthSignOutRequested);
    on<AuthSessionRefreshRequested>(_onAuthSessionRefreshRequested);
    on<AuthOAuthSignInRequested>(_onAuthOAuthSignInRequested);
    on<AuthAccountDeletionRequested>(_onAuthAccountDeletionRequested);
    on<AuthEmailConfirmationResendRequested>(_onAuthEmailConfirmationResendRequested);
    on<AuthStateChanged>(_onAuthStateChanged);
    on<AuthErrorCleared>(_onAuthErrorCleared);

    // Listen to authentication state changes
    _authStateSubscription = _authRepository.authStateChanges.listen(
      (authEntity) {
        add(AuthStateChanged(
          isAuthenticated: authEntity.isAuthenticated,
          userId: authEntity.userId,
          email: authEntity.email,
        ));
      },
    );

    // Check initial authentication status
    add(const AuthCheckRequested());
  }

  @override
  Future<void> close() {
    _authStateSubscription.cancel();
    return super.close();
  }

  Future<void> _onAuthCheckRequested(
    AuthCheckRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.getCurrentSession();
    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (authEntity) async {
        if (authEntity != null && authEntity.isValid) {
          // Get current user
          final userResult = await _authRepository.getCurrentUser();
          userResult.fold(
            (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
            (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
          );
        } else {
          emit(const AuthUnauthenticated());
        }
      },
    );
  }

  Future<void> _onAuthSignInRequested(
    AuthSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _loginUser(LoginUserParams(
      email: event.email,
      password: event.password,
    ));

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (authEntity) async {
        // Get current user
        final userResult = await _authRepository.getCurrentUser();
        userResult.fold(
          (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
          (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
        );
      },
    );
  }

  Future<void> _onAuthSignUpRequested(
    AuthSignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _registerUser(RegisterUserParams(
      email: event.email,
      password: event.password,
      fullName: event.fullName,
      username: event.username,
    ));

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (authEntity) {
        if (authEntity.isAuthenticated) {
          // User is immediately authenticated (auto-confirm is enabled)
          emit(AuthAuthenticated(authEntity: authEntity));
        } else {
          // User needs to confirm email
          emit(AuthSignUpSuccess(
            email: event.email,
            message: 'Please check your email to confirm your account.',
          ));
        }
      },
    );
  }

  Future<void> _onAuthMagicLinkRequested(
    AuthMagicLinkRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.signInWithMagicLink(
      email: event.email,
      redirectTo: event.redirectTo,
    );

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(AuthMagicLinkSent(
        email: event.email,
        message: 'Magic link sent to your email. Please check your inbox.',
      )),
    );
  }

  Future<void> _onAuthOTPVerificationRequested(
    AuthOTPVerificationRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.verifyOTP(
      email: event.email,
      token: event.token,
      type: event.type,
    );

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (authEntity) async {
        // Get current user
        final userResult = await _authRepository.getCurrentUser();
        userResult.fold(
          (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
          (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
        );
      },
    );
  }

  Future<void> _onAuthPasswordResetRequested(
    AuthPasswordResetRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _resetPassword(ResetPasswordParams(
      email: event.email,
      redirectTo: event.redirectTo,
    ));

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(AuthPasswordResetSent(
        email: event.email,
        message: 'Password reset link sent to your email.',
      )),
    );
  }

  Future<void> _onAuthPasswordUpdateRequested(
    AuthPasswordUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.updatePassword(
      newPassword: event.newPassword,
      accessToken: event.accessToken,
    );

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(const AuthPasswordUpdated(
        message: 'Password updated successfully.',
      )),
    );
  }

  Future<void> _onAuthSignOutRequested(
    AuthSignOutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _logoutUser(const NoParams());

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(const AuthUnauthenticated()),
    );
  }

  Future<void> _onAuthSessionRefreshRequested(
    AuthSessionRefreshRequested event,
    Emitter<AuthState> emit,
  ) async {
    final result = await _authRepository.refreshSession(
      refreshToken: event.refreshToken,
    );

    result.fold(
      (failure) {
        if (failure.message.contains('expired') || failure.message.contains('invalid')) {
          emit(const AuthSessionExpired(
            message: 'Your session has expired. Please sign in again.',
          ));
        } else {
          emit(AuthError(message: failure.message));
        }
      },
      (authEntity) async {
        // Get current user
        final userResult = await _authRepository.getCurrentUser();
        userResult.fold(
          (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
          (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
        );
      },
    );
  }

  Future<void> _onAuthOAuthSignInRequested(
    AuthOAuthSignInRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.signInWithOAuth(
      provider: event.provider,
      redirectTo: event.redirectTo,
    );

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (authEntity) async {
        if (authEntity.isAuthenticated) {
          // Get current user
          final userResult = await _authRepository.getCurrentUser();
          userResult.fold(
            (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
            (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
          );
        } else {
          // OAuth sign-in initiated, waiting for callback
          emit(const AuthLoading());
        }
      },
    );
  }

  Future<void> _onAuthAccountDeletionRequested(
    AuthAccountDeletionRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.deleteAccount();

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(const AuthAccountDeleted(
        message: 'Account deleted successfully.',
      )),
    );
  }

  Future<void> _onAuthEmailConfirmationResendRequested(
    AuthEmailConfirmationResendRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _authRepository.resendEmailConfirmation(
      email: event.email,
    );

    result.fold(
      (failure) => emit(AuthError(message: failure.message)),
      (_) => emit(AuthEmailConfirmationResent(
        email: event.email,
        message: 'Confirmation email resent. Please check your inbox.',
      )),
    );
  }

  Future<void> _onAuthStateChanged(
    AuthStateChanged event,
    Emitter<AuthState> emit,
  ) async {
    if (event.isAuthenticated) {
      // Get current session and user
      final sessionResult = await _authRepository.getCurrentSession();
      sessionResult.fold(
        (failure) => emit(AuthError(message: failure.message)),
        (authEntity) async {
          if (authEntity != null) {
            final userResult = await _authRepository.getCurrentUser();
            userResult.fold(
              (failure) => emit(AuthAuthenticated(authEntity: authEntity)),
              (user) => emit(AuthAuthenticated(authEntity: authEntity, user: user)),
            );
          } else {
            emit(const AuthUnauthenticated());
          }
        },
      );
    } else {
      emit(const AuthUnauthenticated());
    }
  }

  void _onAuthErrorCleared(
    AuthErrorCleared event,
    Emitter<AuthState> emit,
  ) {
    if (state is AuthError) {
      emit(const AuthUnauthenticated());
    }
  }
}
