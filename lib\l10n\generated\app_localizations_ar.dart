/// Generated file. Do not edit.
///
/// Localization for Sijilli App
/// Arabic (ar) and English (en) support


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'سجلي';

  @override
  String get appDescription => 'تطبيق إدارة المواعيد والمقالات';

  @override
  String get welcome => 'مرحباً';

  @override
  String get welcomeToSijilli => 'مرحباً بك في سجلي';

  @override
  String get appUnderDevelopment => 'التطبيق قيد التطوير';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get create => 'إنشاء';

  @override
  String get update => 'تحديث';

  @override
  String get confirm => 'تأكيد';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get done => 'تم';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get back => 'رجوع';

  @override
  String get close => 'إغلاق';

  @override
  String get search => 'بحث';

  @override
  String get searchHint => 'البحث...';

  @override
  String get noResults => 'لا توجد نتائج';

  @override
  String get emptyList => 'لا توجد عناصر للعرض';

  @override
  String get networkError => 'خطأ في الشبكة';

  @override
  String get unknownError => 'حدث خطأ غير متوقع';

  @override
  String get validationError => 'يرجى التحقق من البيانات المدخلة';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get username => 'اسم المستخدم';

  @override
  String get fullName => 'الاسم الكامل';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get signUp => 'إنشاء حساب جديد';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get emailHint => 'أدخل بريدك الإلكتروني';

  @override
  String get passwordHint => 'أدخل كلمة المرور';

  @override
  String get confirmPasswordHint => 'أعد إدخال كلمة المرور';

  @override
  String get usernameHint => 'أدخل اسم المستخدم';

  @override
  String get fullNameHint => 'أدخل اسمك الكامل';

  @override
  String get emailRequired => 'البريد الإلكتروني مطلوب';

  @override
  String get passwordRequired => 'كلمة المرور مطلوبة';

  @override
  String get usernameRequired => 'اسم المستخدم مطلوب';

  @override
  String get fullNameRequired => 'الاسم الكامل مطلوب';

  @override
  String get invalidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get passwordTooShort => 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get usernameInvalid => 'اسم المستخدم يجب أن يكون 3-24 حرف ويحتوي على أحرف وأرقام فقط';

  @override
  String get signInTitle => 'مرحباً بعودتك';

  @override
  String get signInSubtitle => 'سجل دخولك للمتابعة';

  @override
  String get signUpTitle => 'إنشاء حساب جديد';

  @override
  String get signUpSubtitle => 'انضم إلى مجتمع سجلي';

  @override
  String get forgotPasswordTitle => 'نسيت كلمة المرور';

  @override
  String get forgotPasswordSubtitle => 'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get backToSignIn => 'العودة لتسجيل الدخول';

  @override
  String get signInWithGoogle => 'تسجيل الدخول بجوجل';

  @override
  String get signInWithApple => 'تسجيل الدخول بآبل';

  @override
  String get signInWithFacebook => 'تسجيل الدخول بفيسبوك';

  @override
  String get orContinueWith => 'أو تابع باستخدام';

  @override
  String get sendResetLink => 'إرسال رابط الإعادة';

  @override
  String get resetLinkSent => 'تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني';

  @override
  String get checkYourEmail => 'تحقق من بريدك الإلكتروني';

  @override
  String get emailConfirmationSent => 'تم إرسال رابط التأكيد إلى بريدك الإلكتروني';

  @override
  String get resendConfirmation => 'إعادة إرسال التأكيد';

  @override
  String get signInSuccess => 'تم تسجيل الدخول بنجاح';

  @override
  String get signUpSuccess => 'تم إنشاء الحساب بنجاح';

  @override
  String get signOutSuccess => 'تم تسجيل الخروج بنجاح';

  @override
  String get passwordResetSuccess => 'تم إرسال رابط إعادة التعيين';

  @override
  String get passwordUpdateSuccess => 'تم تحديث كلمة المرور بنجاح';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get invalidCredentials => 'بيانات الدخول غير صحيحة';

  @override
  String get userNotFound => 'المستخدم غير موجود';

  @override
  String get emailAlreadyExists => 'البريد الإلكتروني مستخدم بالفعل';

  @override
  String get usernameAlreadyExists => 'اسم المستخدم مستخدم بالفعل';

  @override
  String get weakPassword => 'كلمة المرور ضعيفة';

  @override
  String get tooManyRequests => 'محاولات كثيرة، يرجى المحاولة لاحقاً';

  @override
  String get sessionExpired => 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى';

  @override
  String get phone => 'رقم الهاتف';

  @override
  String get bio => 'النبذة الشخصية';

  @override
  String get profession => 'المهنة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get lightTheme => 'فاتح';

  @override
  String get darkTheme => 'داكن';

  @override
  String get systemTheme => 'النظام';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get appointments => 'المواعيد';

  @override
  String get createAppointment => 'إنشاء موعد';

  @override
  String get editAppointment => 'تعديل الموعد';

  @override
  String get appointmentDetails => 'تفاصيل الموعد';

  @override
  String get title => 'العنوان';

  @override
  String get description => 'الوصف';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get startTime => 'وقت البداية';

  @override
  String get endTime => 'وقت النهاية';

  @override
  String get location => 'الموقع';

  @override
  String get region => 'المنطقة';

  @override
  String get building => 'المبنى';

  @override
  String get allDay => 'طوال اليوم';

  @override
  String get isPublic => 'عام';

  @override
  String get articles => 'المقالات';

  @override
  String get createArticle => 'إنشاء مقال';

  @override
  String get editArticle => 'تعديل المقال';

  @override
  String get articleDetails => 'تفاصيل المقال';

  @override
  String get content => 'المحتوى';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get noNotifications => 'لا توجد إشعارات';

  @override
  String get markAsRead => 'تحديد كمقروء';

  @override
  String get markAllAsRead => 'تحديد الكل كمقروء';

  @override
  String get followers => 'المتابعون';

  @override
  String get following => 'المتابَعون';

  @override
  String get follow => 'متابعة';

  @override
  String get unfollow => 'إلغاء المتابعة';

  @override
  String get today => 'اليوم';

  @override
  String get tomorrow => 'غداً';

  @override
  String get yesterday => 'أمس';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get hijriDate => 'التاريخ الهجري';

  @override
  String get gregorianDate => 'التاريخ الميلادي';

  @override
  String get hijriCorrection => 'تصحيح التاريخ الهجري';

  @override
  String get organization => 'مؤسسة';

  @override
  String get verified => 'موثق';

  @override
  String get repost => 'إعادة نشر';

  @override
  String get share => 'مشاركة';

  @override
  String get link => 'رابط';

  @override
  String get url => 'الرابط';

  @override
  String get website => 'الموقع الإلكتروني';

  @override
  String get socialLinks => 'الروابط الاجتماعية';

  @override
  String get avatar => 'الصورة الشخصية';

  @override
  String get banner => 'صورة الغلاف';

  @override
  String get uploadImage => 'رفع صورة';

  @override
  String get changeImage => 'تغيير الصورة';

  @override
  String get removeImage => 'إزالة الصورة';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get selectImageSource => 'اختر مصدر الصورة';

  @override
  String get permissionDenied => 'تم رفض الإذن';

  @override
  String get cameraPermissionRequired => 'إذن الكاميرا مطلوب';

  @override
  String get storagePermissionRequired => 'إذن التخزين مطلوب';

  @override
  String get deleteConfirmation => 'هل أنت متأكد من حذف هذا العنصر؟';

  @override
  String get logoutConfirmation => 'هل أنت متأكد من تسجيل الخروج؟';

  @override
  String get unsavedChanges => 'لديك تغييرات غير محفوظة. هل تريد تجاهلها؟';

  @override
  String get discard => 'تجاهل';

  @override
  String get keepEditing => 'متابعة التعديل';
}
