import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/constants/app_constants.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../shared/widgets/language_selector.dart';
import '../../providers/localization/localization_provider.dart';

/// Settings screen for app configuration
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).settings),
        actions: [
          LanguageToggleButton(
            onLanguageChanged: () {
              // Refresh the screen after language change
              if (context.mounted) {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: Consumer<LocalizationProvider>(
        builder: (context, localizationProvider, child) {
          final l10n = AppLocalizations.of(context);

          return ListView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            children: [
              // Language Section
              _buildSectionHeader(context, l10n.language, Icons.language),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: LanguageSelector(
                    showTitle: false,
                    onLanguageChanged: () {
                      // Optional: Add any additional logic after language change
                    },
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Theme Section
              _buildSectionHeader(context, l10n.theme, Icons.palette),
              const SizedBox(height: 16),
              Card(
                child: Column(
                  children: [
                    _buildThemeOption(
                      context,
                      l10n.lightTheme,
                      Icons.light_mode,
                      ThemeMode.light,
                      ThemeMode.system, // Current theme mode (placeholder)
                      () {
                        // TODO: Implement theme switching
                      },
                    ),
                    const Divider(height: 1),
                    _buildThemeOption(
                      context,
                      l10n.darkTheme,
                      Icons.dark_mode,
                      ThemeMode.dark,
                      ThemeMode.system, // Current theme mode (placeholder)
                      () {
                        // TODO: Implement theme switching
                      },
                    ),
                    const Divider(height: 1),
                    _buildThemeOption(
                      context,
                      l10n.systemTheme,
                      Icons.settings_system_daydream,
                      ThemeMode.system,
                      ThemeMode.system, // Current theme mode (placeholder)
                      () {
                        // TODO: Implement theme switching
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Calendar Section
              _buildSectionHeader(
                context,
                'التقويم / Calendar',
                Icons.calendar_today,
              ),
              const SizedBox(height: 16),
              Card(
                child: Column(
                  children: [
                    _buildSettingsTile(
                      context,
                      l10n.hijriCorrection,
                      '0 ${localizationProvider.currentLanguageCode == 'ar' ? 'يوم' : 'days'}',
                      Icons.date_range,
                      () {
                        // TODO: Implement Hijri correction setting
                      },
                    ),
                    const Divider(height: 1),
                    _buildSettingsTile(
                      context,
                      'تنسيق الأرقام / Number Format',
                      localizationProvider.currentLanguageCode == 'ar'
                          ? 'عربي'
                          : 'Arabic',
                      Icons.format_list_numbered,
                      () {
                        // TODO: Implement number format setting
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // About Section
              _buildSectionHeader(context, 'حول التطبيق / About', Icons.info),
              const SizedBox(height: 16),
              Card(
                child: Column(
                  children: [
                    _buildSettingsTile(
                      context,
                      'الإصدار / Version',
                      AppConstants.appVersion,
                      Icons.info_outline,
                      null,
                    ),
                    const Divider(height: 1),
                    _buildSettingsTile(
                      context,
                      'الشروط والأحكام / Terms & Conditions',
                      '',
                      Icons.description,
                      () {
                        // TODO: Navigate to terms screen
                      },
                    ),
                    const Divider(height: 1),
                    _buildSettingsTile(
                      context,
                      'سياسة الخصوصية / Privacy Policy',
                      '',
                      Icons.privacy_tip,
                      () {
                        // TODO: Navigate to privacy policy screen
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Quick Language Switch Demo
              Center(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    await localizationProvider.switchLanguage();
                  },
                  icon: const Icon(Icons.translate),
                  label: Text(
                    localizationProvider.currentLanguageCode == 'ar'
                        ? 'التبديل إلى الإنجليزية'
                        : 'Switch to Arabic',
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Language Selection Dialog Demo
              Center(
                child: OutlinedButton.icon(
                  onPressed: () {
                    LanguageSelectionDialog.show(context);
                  },
                  icon: const Icon(Icons.language),
                  label: Text(
                    localizationProvider.currentLanguageCode == 'ar'
                        ? 'اختيار اللغة'
                        : 'Select Language',
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primary, size: 24),
        const SizedBox(width: 12),
        Text(
          title,
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback? onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppColors.onSurfaceVariant),
      title: Text(title, style: AppTextStyles.bodyLarge),
      subtitle:
          subtitle.isNotEmpty
              ? Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              )
              : null,
      trailing:
          onTap != null
              ? const Icon(
                Icons.chevron_right,
                color: AppColors.onSurfaceVariant,
              )
              : null,
      onTap: onTap,
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String title,
    IconData icon,
    ThemeMode themeMode,
    ThemeMode currentThemeMode,
    VoidCallback onTap,
  ) {
    final isSelected = themeMode == currentThemeMode;

    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          color: isSelected ? AppColors.primary : AppColors.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      trailing:
          isSelected ? const Icon(Icons.check, color: AppColors.primary) : null,
      onTap: onTap,
    );
  }
}
